{"archive": {}, "artifacts": [{"path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/lib/libmuduo_http.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "include_directories"], "files": ["muduo/net/http/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}, {"command": 2, "file": 0, "line": 7, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 71, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}], "language": "CXX", "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 3, "id": "muduo_base::@af02cc837cde80c48ca0"}, {"backtrace": 3, "id": "muduo_net::@d48a3526fec38d23c536"}], "id": "muduo_http::@5787850c391ef1a35796", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/tuchuang"}}, "name": "muduo_http", "nameOnDisk": "libmuduo_http.a", "paths": {"build": "muduo/net/http", "source": "muduo/net/http"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/http/HttpServer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/http/HttpContext.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
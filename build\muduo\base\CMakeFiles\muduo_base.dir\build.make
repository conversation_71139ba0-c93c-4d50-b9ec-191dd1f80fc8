# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include muduo/base/CMakeFiles/muduo_base.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.make

# Include the progress variables for this target.
include muduo/base/CMakeFiles/muduo_base.dir/progress.make

# Include the compile flags for this target's objects.
include muduo/base/CMakeFiles/muduo_base.dir/flags.make

muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj: ../muduo/base/AsyncLogging.cc
muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj -MF CMakeFiles\muduo_base.dir\AsyncLogging.cc.obj.d -o CMakeFiles\muduo_base.dir\AsyncLogging.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\AsyncLogging.cc

muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/AsyncLogging.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\AsyncLogging.cc > CMakeFiles\muduo_base.dir\AsyncLogging.cc.i

muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/AsyncLogging.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\AsyncLogging.cc -o CMakeFiles\muduo_base.dir\AsyncLogging.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj: ../muduo/base/Condition.cc
muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj -MF CMakeFiles\muduo_base.dir\Condition.cc.obj.d -o CMakeFiles\muduo_base.dir\Condition.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Condition.cc

muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Condition.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Condition.cc > CMakeFiles\muduo_base.dir\Condition.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Condition.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Condition.cc -o CMakeFiles\muduo_base.dir\Condition.cc.s

muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj: ../muduo/base/CountDownLatch.cc
muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj -MF CMakeFiles\muduo_base.dir\CountDownLatch.cc.obj.d -o CMakeFiles\muduo_base.dir\CountDownLatch.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CountDownLatch.cc

muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/CountDownLatch.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CountDownLatch.cc > CMakeFiles\muduo_base.dir\CountDownLatch.cc.i

muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/CountDownLatch.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CountDownLatch.cc -o CMakeFiles\muduo_base.dir\CountDownLatch.cc.s

muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj: ../muduo/base/CurrentThread.cc
muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj -MF CMakeFiles\muduo_base.dir\CurrentThread.cc.obj.d -o CMakeFiles\muduo_base.dir\CurrentThread.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CurrentThread.cc

muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/CurrentThread.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CurrentThread.cc > CMakeFiles\muduo_base.dir\CurrentThread.cc.i

muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/CurrentThread.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\CurrentThread.cc -o CMakeFiles\muduo_base.dir\CurrentThread.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj: ../muduo/base/Date.cc
muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj -MF CMakeFiles\muduo_base.dir\Date.cc.obj.d -o CMakeFiles\muduo_base.dir\Date.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Date.cc

muduo/base/CMakeFiles/muduo_base.dir/Date.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Date.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Date.cc > CMakeFiles\muduo_base.dir\Date.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Date.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Date.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Date.cc -o CMakeFiles\muduo_base.dir\Date.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj: ../muduo/base/Exception.cc
muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj -MF CMakeFiles\muduo_base.dir\Exception.cc.obj.d -o CMakeFiles\muduo_base.dir\Exception.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Exception.cc

muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Exception.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Exception.cc > CMakeFiles\muduo_base.dir\Exception.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Exception.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Exception.cc -o CMakeFiles\muduo_base.dir\Exception.cc.s

muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj: ../muduo/base/FileUtil.cc
muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj -MF CMakeFiles\muduo_base.dir\FileUtil.cc.obj.d -o CMakeFiles\muduo_base.dir\FileUtil.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\FileUtil.cc

muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/FileUtil.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\FileUtil.cc > CMakeFiles\muduo_base.dir\FileUtil.cc.i

muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/FileUtil.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\FileUtil.cc -o CMakeFiles\muduo_base.dir\FileUtil.cc.s

muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj: ../muduo/base/LogFile.cc
muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj -MF CMakeFiles\muduo_base.dir\LogFile.cc.obj.d -o CMakeFiles\muduo_base.dir\LogFile.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogFile.cc

muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/LogFile.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogFile.cc > CMakeFiles\muduo_base.dir\LogFile.cc.i

muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/LogFile.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogFile.cc -o CMakeFiles\muduo_base.dir\LogFile.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj: ../muduo/base/Logging.cc
muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj -MF CMakeFiles\muduo_base.dir\Logging.cc.obj.d -o CMakeFiles\muduo_base.dir\Logging.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Logging.cc

muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Logging.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Logging.cc > CMakeFiles\muduo_base.dir\Logging.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Logging.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Logging.cc -o CMakeFiles\muduo_base.dir\Logging.cc.s

muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj: ../muduo/base/LogStream.cc
muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj -MF CMakeFiles\muduo_base.dir\LogStream.cc.obj.d -o CMakeFiles\muduo_base.dir\LogStream.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogStream.cc

muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/LogStream.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogStream.cc > CMakeFiles\muduo_base.dir\LogStream.cc.i

muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/LogStream.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\LogStream.cc -o CMakeFiles\muduo_base.dir\LogStream.cc.s

muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj: ../muduo/base/ProcessInfo.cc
muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj -MF CMakeFiles\muduo_base.dir\ProcessInfo.cc.obj.d -o CMakeFiles\muduo_base.dir\ProcessInfo.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ProcessInfo.cc

muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/ProcessInfo.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ProcessInfo.cc > CMakeFiles\muduo_base.dir\ProcessInfo.cc.i

muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/ProcessInfo.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ProcessInfo.cc -o CMakeFiles\muduo_base.dir\ProcessInfo.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj: ../muduo/base/Timestamp.cc
muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj -MF CMakeFiles\muduo_base.dir\Timestamp.cc.obj.d -o CMakeFiles\muduo_base.dir\Timestamp.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Timestamp.cc

muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Timestamp.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Timestamp.cc > CMakeFiles\muduo_base.dir\Timestamp.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Timestamp.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Timestamp.cc -o CMakeFiles\muduo_base.dir\Timestamp.cc.s

muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj: ../muduo/base/Thread.cc
muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj -MF CMakeFiles\muduo_base.dir\Thread.cc.obj.d -o CMakeFiles\muduo_base.dir\Thread.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Thread.cc

muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/Thread.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Thread.cc > CMakeFiles\muduo_base.dir\Thread.cc.i

muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/Thread.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\Thread.cc -o CMakeFiles\muduo_base.dir\Thread.cc.s

muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj: ../muduo/base/ThreadPool.cc
muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj -MF CMakeFiles\muduo_base.dir\ThreadPool.cc.obj.d -o CMakeFiles\muduo_base.dir\ThreadPool.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ThreadPool.cc

muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/ThreadPool.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ThreadPool.cc > CMakeFiles\muduo_base.dir\ThreadPool.cc.i

muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/ThreadPool.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\ThreadPool.cc -o CMakeFiles\muduo_base.dir\ThreadPool.cc.s

muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj: ../muduo/base/TimeZone.cc
muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj -MF CMakeFiles\muduo_base.dir\TimeZone.cc.obj.d -o CMakeFiles\muduo_base.dir\TimeZone.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\TimeZone.cc

muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/TimeZone.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\TimeZone.cc > CMakeFiles\muduo_base.dir\TimeZone.cc.i

muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/TimeZone.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\TimeZone.cc -o CMakeFiles\muduo_base.dir\TimeZone.cc.s

muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/flags.make
muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/includes_CXX.rsp
muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj: ../muduo/base/md5.cc
muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj: muduo/base/CMakeFiles/muduo_base.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj -MF CMakeFiles\muduo_base.dir\md5.cc.obj.d -o CMakeFiles\muduo_base.dir\md5.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\md5.cc

muduo/base/CMakeFiles/muduo_base.dir/md5.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_base.dir/md5.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\md5.cc > CMakeFiles\muduo_base.dir\md5.cc.i

muduo/base/CMakeFiles/muduo_base.dir/md5.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_base.dir/md5.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base\md5.cc -o CMakeFiles\muduo_base.dir\md5.cc.s

# Object files for target muduo_base
muduo_base_OBJECTS = \
"CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj" \
"CMakeFiles/muduo_base.dir/Condition.cc.obj" \
"CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj" \
"CMakeFiles/muduo_base.dir/CurrentThread.cc.obj" \
"CMakeFiles/muduo_base.dir/Date.cc.obj" \
"CMakeFiles/muduo_base.dir/Exception.cc.obj" \
"CMakeFiles/muduo_base.dir/FileUtil.cc.obj" \
"CMakeFiles/muduo_base.dir/LogFile.cc.obj" \
"CMakeFiles/muduo_base.dir/Logging.cc.obj" \
"CMakeFiles/muduo_base.dir/LogStream.cc.obj" \
"CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj" \
"CMakeFiles/muduo_base.dir/Timestamp.cc.obj" \
"CMakeFiles/muduo_base.dir/Thread.cc.obj" \
"CMakeFiles/muduo_base.dir/ThreadPool.cc.obj" \
"CMakeFiles/muduo_base.dir/TimeZone.cc.obj" \
"CMakeFiles/muduo_base.dir/md5.cc.obj"

# External object files for target muduo_base
muduo_base_EXTERNAL_OBJECTS =

../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/build.make
../muduo/lib/libmuduo_base.a: muduo/base/CMakeFiles/muduo_base.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Linking CXX static library ..\..\..\muduo\lib\libmuduo_base.a"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && $(CMAKE_COMMAND) -P CMakeFiles\muduo_base.dir\cmake_clean_target.cmake
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\muduo_base.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
muduo/base/CMakeFiles/muduo_base.dir/build: ../muduo/lib/libmuduo_base.a
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/build

muduo/base/CMakeFiles/muduo_base.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base && $(CMAKE_COMMAND) -P CMakeFiles\muduo_base.dir\cmake_clean.cmake
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/clean

muduo/base/CMakeFiles/muduo_base.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\base C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base\CMakeFiles\muduo_base.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/depend


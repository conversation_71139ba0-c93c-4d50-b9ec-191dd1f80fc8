{"archive": {}, "artifacts": [{"path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/lib/libmuduo_net.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "include_directories"], "files": ["muduo/net/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 30, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 0, "line": 31, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 71, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18]}, {"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}, {"fragment": "-DNO_ACCEPT4"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}], "language": "CXX", "sourceIndexes": [13]}], "dependencies": [{"backtrace": 3, "id": "muduo_base::@af02cc837cde80c48ca0"}], "id": "muduo_net::@d48a3526fec38d23c536", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/tuchuang"}}, "name": "muduo_net", "nameOnDisk": "libmuduo_net.a", "paths": {"build": "muduo/net", "source": "muduo/net"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Acceptor.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Buffer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Channel.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Connector.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/EventLoop.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/EventLoopThread.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/EventLoopThreadPool.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/InetAddress.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Poller.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/poller/DefaultPoller.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/poller/EPollPoller.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/poller/PollPoller.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Socket.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "muduo/net/SocketsOps.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/TcpClient.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/TcpConnection.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/TcpServer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/Timer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/net/TimerQueue.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
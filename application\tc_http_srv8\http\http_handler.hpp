// bool handleFileUpload(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
//     // 获取 HttpContext
//     auto httpContext = std::any_cast<std::shared_ptr<HttpContext>>(conn->getContext());
//     if (!httpContext) {
//         LOG_ERROR << "HttpContext is null";
//         sendError(resp, "Internal Server Error", HttpResponse::k500InternalServerError, conn);
//         return true;
//     }

//     // 尝试获取已存在的上传上下文
//     std::shared_ptr<FileUploadContext> uploadContext = httpContext->getContext<FileUploadContext>();

//     if (!uploadContext) {
//         // 解析 multipart/form-data 边界
//         std::string contentType = req.getHeader("Content-Type");
//         if (contentType.empty()) {
//             sendError(resp, "Content-Type header is missing", HttpResponse::k400BadRequest, conn);
//             return true;
//         }
        
//         std::regex boundaryRegex("boundary=(.+)$");
//         std::smatch matches;
//         if (!std::regex_search(contentType, matches, boundaryRegex)) {
//             sendError(resp, "Invalid Content-Type", HttpResponse::k400BadRequest, conn);
//             return true;
//         }
//         std::string boundary = "--" + matches[1].str();
//         std::cout << "Boundary: " << boundary;

//         try {
//             std::string body = req.body();
//             if (body.empty()) {
//                 sendError(resp, "Request body is empty", HttpResponse::k400BadRequest, conn);
//                 return true;
//             }

//             // 使用指针记录当前处理位置
//             size_t currentPos = 0;

//             // 1. 解析文件名
//             size_t fileStart = body.find(boundary, currentPos);
//             if (fileStart == std::string::npos) {
//                 sendError(resp, "File boundary not found", HttpResponse::k400BadRequest, conn);
//                 return true;
//             }
//             currentPos = fileStart + boundary.length();

//             // 创建一个临时字符串来存储当前处理的部分
//             std::string currentPart = body.substr(currentPos);
//             std::regex filenameRegex("filename=\"([^\"]+)\"");
//             if (!std::regex_search(currentPart, matches, filenameRegex) || !matches[1].matched) {
//                 sendError(resp, "Filename not found", HttpResponse::k400BadRequest, conn);
//                 return true;
//             }
//             std::string originalFilename = matches[1].str();
//             std::cout << "Found filename: " << originalFilename;

//             // 更新 currentPos 到文件名之后
//             currentPos += matches[0].length();

//             // 创建上传上下文
//             uploadContext = std::make_shared<FileUploadContext>(originalFilename);
//             if(uploadContext->connect("/etc/fastdfs/client.conf")) {
//                 std::cout << "connect to fastdfs success";
//             } else {
//                 LOG_ERROR << "connect to fastdfs failed";
//                 sendError(resp, "Failed to connect to FastDFS", HttpResponse::k500InternalServerError, conn);
//                 return true;
//             }
            
//             httpContext->setContext(uploadContext);
//             uploadContext->setBoundary(boundary);

//             // 2. 解析文件内容
//             currentPos = body.find("\r\n\r\n", currentPos);
//             if (currentPos != std::string::npos) {
//                 currentPos += 4;
//                 size_t nextBoundary = body.find(boundary, currentPos);
//                 if (nextBoundary != std::string::npos) {
//                     // 上传文件内容
//                     uploadContext->uploadBlock(body.data() + currentPos, nextBoundary - currentPos);
//                     std::cout << "Uploaded file content, size: " << nextBoundary - currentPos;
//                     currentPos = nextBoundary + boundary.length();
//                 }
//             }

//             // 3. 解析 user
//             std::string userField = "name=\"user\"";
//             size_t userStart = body.find(userField, currentPos);
//             if (userStart != std::string::npos) {
//                 userStart = body.find("\r\n\r\n", userStart);
//                 if (userStart != std::string::npos) {
//                     userStart += 4;  // 跳过 \r\n\r\n
//                     size_t userEnd = body.find("\r\n", userStart);
//                     if (userEnd != std::string::npos) {
//                         std::string user = body.substr(userStart, userEnd - userStart);
//                         std::cout << "Found user: " << user;
//                         currentPos = userEnd + 2;  // 跳过换行符
//                     }
//                 }
//             }

//             // 4. 解析 md5
//             std::string md5Field = "name=\"md5\"";
//             size_t md5Start = body.find(md5Field, currentPos);
//             if (md5Start != std::string::npos) {
//                 md5Start = body.find("\r\n\r\n", md5Start);
//                 if (md5Start != std::string::npos) {
//                     md5Start += 4;  // 跳过 \r\n\r\n
//                     size_t md5End = body.find("\r\n", md5Start);
//                     if (md5End != std::string::npos) {
//                         std::string md5 = body.substr(md5Start, md5End - md5Start);
//                         std::cout << "Found md5: " << md5;
//                         currentPos = md5End + 2;  // 跳过换行符
//                     }
//                 }
//             }

//             // 5. 解析 size
//             std::string sizeField = "name=\"size\"";
//             size_t sizeStart = body.find(sizeField, currentPos);
//             if (sizeStart != std::string::npos) {
//                 sizeStart = body.find("\r\n\r\n", sizeStart);
//                 if (sizeStart != std::string::npos) {
//                     sizeStart += 4;  // 跳过 \r\n\r\n
//                     size_t sizeEnd = body.find("\r\n", sizeStart);
//                     if (sizeEnd != std::string::npos) {
//                         std::string sizeStr = body.substr(sizeStart, sizeEnd - sizeStart);
//                         std::cout << "Found size: " << sizeStr;
//                     }
//                 }
//             }

//             uploadContext->setState(FileUploadContext::State::kComplete);
//             req.setBody(""); // 清空请求体
            
//             // 返回成功响应
//             resp->setStatusCode(HttpResponse::k200Ok);
//             resp->setStatusMessage("OK");
//             resp->setContentType("application/json");
//             resp->setBody("{\"message\": \"File uploaded successfully\"}");
            
//             return true;
//         } catch (const std::exception& e) {
//             LOG_ERROR << "Failed to process upload: " << e.what();
//             sendError(resp, "Failed to process upload", HttpResponse::k500InternalServerError, conn);
//             return true;
//         }
//     }

//     return false;
// } 
Determining if the function accept4 exists failed with the following output:
Change Dir: C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/CMakeFiles/CMakeTmp

Run Build Command(s):C:/msys64/ucrt64/bin/mingw32-make.exe -f Makefile cmTC_ec74f/fast && C:/msys64/ucrt64/bin/mingw32-make.exe  -f CMakeFiles\cmTC_ec74f.dir\build.make CMakeFiles/cmTC_ec74f.dir/build

mingw32-make[1]: Entering directory 'C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_ec74f.dir/CheckFunctionExists.c.obj

C:\msys64\ucrt64\bin\gcc.exe   -DCHECK_FUNCTION_EXISTS=accept4 -o CMakeFiles\cmTC_ec74f.dir\CheckFunctionExists.c.obj -c "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CheckFunctionExists.c"

Linking C executable cmTC_ec74f.exe

"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_ec74f.dir\link.txt --verbose=1

"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f CMakeFiles\cmTC_ec74f.dir/objects.a
C:\msys64\ucrt64\bin\ar.exe cr CMakeFiles\cmTC_ec74f.dir/objects.a @CMakeFiles\cmTC_ec74f.dir\objects1.rsp
C:\msys64\ucrt64\bin\gcc.exe  -DCHECK_FUNCTION_EXISTS=accept4 -Wl,--whole-archive CMakeFiles\cmTC_ec74f.dir/objects.a -Wl,--no-whole-archive -o cmTC_ec74f.exe -Wl,--out-implib,libcmTC_ec74f.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_ec74f.dir\linklibs.rsp
C:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe: CMakeFiles\cmTC_ec74f.dir/objects.a(CheckFunctionExists.c.obj):CheckFunctionExists.c:(.text+0x15): undefined reference to `accept4'

collect2.exe: error: ld returned 1 exit status
mingw32-make[1]: *** [CMakeFiles\cmTC_ec74f.dir\build.make:100: cmTC_ec74f.exe] Error 1

mingw32-make[1]: Leaving directory 'C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/CMakeFiles/CMakeTmp'

mingw32-make: *** [Makefile:126: cmTC_ec74f/fast] Error 2





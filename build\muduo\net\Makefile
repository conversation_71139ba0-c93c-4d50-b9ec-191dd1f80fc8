# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\\CMakeFiles\progress.marks
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
muduo/net/CMakeFiles/muduo_net.dir/rule:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/CMakeFiles/muduo_net.dir/rule
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/rule

# Convenience name for target.
muduo_net: muduo/net/CMakeFiles/muduo_net.dir/rule
.PHONY : muduo_net

# fast build rule for target.
muduo_net/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/build
.PHONY : muduo_net/fast

Acceptor.obj: Acceptor.cc.obj
.PHONY : Acceptor.obj

# target to build an object file
Acceptor.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj
.PHONY : Acceptor.cc.obj

Acceptor.i: Acceptor.cc.i
.PHONY : Acceptor.i

# target to preprocess a source file
Acceptor.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.i
.PHONY : Acceptor.cc.i

Acceptor.s: Acceptor.cc.s
.PHONY : Acceptor.s

# target to generate assembly for a file
Acceptor.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.s
.PHONY : Acceptor.cc.s

Buffer.obj: Buffer.cc.obj
.PHONY : Buffer.obj

# target to build an object file
Buffer.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj
.PHONY : Buffer.cc.obj

Buffer.i: Buffer.cc.i
.PHONY : Buffer.i

# target to preprocess a source file
Buffer.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.i
.PHONY : Buffer.cc.i

Buffer.s: Buffer.cc.s
.PHONY : Buffer.s

# target to generate assembly for a file
Buffer.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.s
.PHONY : Buffer.cc.s

Channel.obj: Channel.cc.obj
.PHONY : Channel.obj

# target to build an object file
Channel.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj
.PHONY : Channel.cc.obj

Channel.i: Channel.cc.i
.PHONY : Channel.i

# target to preprocess a source file
Channel.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.i
.PHONY : Channel.cc.i

Channel.s: Channel.cc.s
.PHONY : Channel.s

# target to generate assembly for a file
Channel.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.s
.PHONY : Channel.cc.s

Connector.obj: Connector.cc.obj
.PHONY : Connector.obj

# target to build an object file
Connector.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj
.PHONY : Connector.cc.obj

Connector.i: Connector.cc.i
.PHONY : Connector.i

# target to preprocess a source file
Connector.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.i
.PHONY : Connector.cc.i

Connector.s: Connector.cc.s
.PHONY : Connector.s

# target to generate assembly for a file
Connector.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.s
.PHONY : Connector.cc.s

EventLoop.obj: EventLoop.cc.obj
.PHONY : EventLoop.obj

# target to build an object file
EventLoop.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj
.PHONY : EventLoop.cc.obj

EventLoop.i: EventLoop.cc.i
.PHONY : EventLoop.i

# target to preprocess a source file
EventLoop.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.i
.PHONY : EventLoop.cc.i

EventLoop.s: EventLoop.cc.s
.PHONY : EventLoop.s

# target to generate assembly for a file
EventLoop.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.s
.PHONY : EventLoop.cc.s

EventLoopThread.obj: EventLoopThread.cc.obj
.PHONY : EventLoopThread.obj

# target to build an object file
EventLoopThread.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj
.PHONY : EventLoopThread.cc.obj

EventLoopThread.i: EventLoopThread.cc.i
.PHONY : EventLoopThread.i

# target to preprocess a source file
EventLoopThread.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.i
.PHONY : EventLoopThread.cc.i

EventLoopThread.s: EventLoopThread.cc.s
.PHONY : EventLoopThread.s

# target to generate assembly for a file
EventLoopThread.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.s
.PHONY : EventLoopThread.cc.s

EventLoopThreadPool.obj: EventLoopThreadPool.cc.obj
.PHONY : EventLoopThreadPool.obj

# target to build an object file
EventLoopThreadPool.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj
.PHONY : EventLoopThreadPool.cc.obj

EventLoopThreadPool.i: EventLoopThreadPool.cc.i
.PHONY : EventLoopThreadPool.i

# target to preprocess a source file
EventLoopThreadPool.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.i
.PHONY : EventLoopThreadPool.cc.i

EventLoopThreadPool.s: EventLoopThreadPool.cc.s
.PHONY : EventLoopThreadPool.s

# target to generate assembly for a file
EventLoopThreadPool.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.s
.PHONY : EventLoopThreadPool.cc.s

InetAddress.obj: InetAddress.cc.obj
.PHONY : InetAddress.obj

# target to build an object file
InetAddress.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj
.PHONY : InetAddress.cc.obj

InetAddress.i: InetAddress.cc.i
.PHONY : InetAddress.i

# target to preprocess a source file
InetAddress.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.i
.PHONY : InetAddress.cc.i

InetAddress.s: InetAddress.cc.s
.PHONY : InetAddress.s

# target to generate assembly for a file
InetAddress.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.s
.PHONY : InetAddress.cc.s

Poller.obj: Poller.cc.obj
.PHONY : Poller.obj

# target to build an object file
Poller.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj
.PHONY : Poller.cc.obj

Poller.i: Poller.cc.i
.PHONY : Poller.i

# target to preprocess a source file
Poller.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.i
.PHONY : Poller.cc.i

Poller.s: Poller.cc.s
.PHONY : Poller.s

# target to generate assembly for a file
Poller.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.s
.PHONY : Poller.cc.s

Socket.obj: Socket.cc.obj
.PHONY : Socket.obj

# target to build an object file
Socket.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj
.PHONY : Socket.cc.obj

Socket.i: Socket.cc.i
.PHONY : Socket.i

# target to preprocess a source file
Socket.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.i
.PHONY : Socket.cc.i

Socket.s: Socket.cc.s
.PHONY : Socket.s

# target to generate assembly for a file
Socket.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.s
.PHONY : Socket.cc.s

SocketsOps.obj: SocketsOps.cc.obj
.PHONY : SocketsOps.obj

# target to build an object file
SocketsOps.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj
.PHONY : SocketsOps.cc.obj

SocketsOps.i: SocketsOps.cc.i
.PHONY : SocketsOps.i

# target to preprocess a source file
SocketsOps.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.i
.PHONY : SocketsOps.cc.i

SocketsOps.s: SocketsOps.cc.s
.PHONY : SocketsOps.s

# target to generate assembly for a file
SocketsOps.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.s
.PHONY : SocketsOps.cc.s

TcpClient.obj: TcpClient.cc.obj
.PHONY : TcpClient.obj

# target to build an object file
TcpClient.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj
.PHONY : TcpClient.cc.obj

TcpClient.i: TcpClient.cc.i
.PHONY : TcpClient.i

# target to preprocess a source file
TcpClient.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.i
.PHONY : TcpClient.cc.i

TcpClient.s: TcpClient.cc.s
.PHONY : TcpClient.s

# target to generate assembly for a file
TcpClient.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.s
.PHONY : TcpClient.cc.s

TcpConnection.obj: TcpConnection.cc.obj
.PHONY : TcpConnection.obj

# target to build an object file
TcpConnection.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj
.PHONY : TcpConnection.cc.obj

TcpConnection.i: TcpConnection.cc.i
.PHONY : TcpConnection.i

# target to preprocess a source file
TcpConnection.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.i
.PHONY : TcpConnection.cc.i

TcpConnection.s: TcpConnection.cc.s
.PHONY : TcpConnection.s

# target to generate assembly for a file
TcpConnection.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.s
.PHONY : TcpConnection.cc.s

TcpServer.obj: TcpServer.cc.obj
.PHONY : TcpServer.obj

# target to build an object file
TcpServer.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj
.PHONY : TcpServer.cc.obj

TcpServer.i: TcpServer.cc.i
.PHONY : TcpServer.i

# target to preprocess a source file
TcpServer.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.i
.PHONY : TcpServer.cc.i

TcpServer.s: TcpServer.cc.s
.PHONY : TcpServer.s

# target to generate assembly for a file
TcpServer.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.s
.PHONY : TcpServer.cc.s

Timer.obj: Timer.cc.obj
.PHONY : Timer.obj

# target to build an object file
Timer.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj
.PHONY : Timer.cc.obj

Timer.i: Timer.cc.i
.PHONY : Timer.i

# target to preprocess a source file
Timer.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.i
.PHONY : Timer.cc.i

Timer.s: Timer.cc.s
.PHONY : Timer.s

# target to generate assembly for a file
Timer.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.s
.PHONY : Timer.cc.s

TimerQueue.obj: TimerQueue.cc.obj
.PHONY : TimerQueue.obj

# target to build an object file
TimerQueue.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj
.PHONY : TimerQueue.cc.obj

TimerQueue.i: TimerQueue.cc.i
.PHONY : TimerQueue.i

# target to preprocess a source file
TimerQueue.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.i
.PHONY : TimerQueue.cc.i

TimerQueue.s: TimerQueue.cc.s
.PHONY : TimerQueue.s

# target to generate assembly for a file
TimerQueue.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.s
.PHONY : TimerQueue.cc.s

poller/DefaultPoller.obj: poller/DefaultPoller.cc.obj
.PHONY : poller/DefaultPoller.obj

# target to build an object file
poller/DefaultPoller.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj
.PHONY : poller/DefaultPoller.cc.obj

poller/DefaultPoller.i: poller/DefaultPoller.cc.i
.PHONY : poller/DefaultPoller.i

# target to preprocess a source file
poller/DefaultPoller.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.i
.PHONY : poller/DefaultPoller.cc.i

poller/DefaultPoller.s: poller/DefaultPoller.cc.s
.PHONY : poller/DefaultPoller.s

# target to generate assembly for a file
poller/DefaultPoller.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.s
.PHONY : poller/DefaultPoller.cc.s

poller/EPollPoller.obj: poller/EPollPoller.cc.obj
.PHONY : poller/EPollPoller.obj

# target to build an object file
poller/EPollPoller.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj
.PHONY : poller/EPollPoller.cc.obj

poller/EPollPoller.i: poller/EPollPoller.cc.i
.PHONY : poller/EPollPoller.i

# target to preprocess a source file
poller/EPollPoller.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.i
.PHONY : poller/EPollPoller.cc.i

poller/EPollPoller.s: poller/EPollPoller.cc.s
.PHONY : poller/EPollPoller.s

# target to generate assembly for a file
poller/EPollPoller.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.s
.PHONY : poller/EPollPoller.cc.s

poller/PollPoller.obj: poller/PollPoller.cc.obj
.PHONY : poller/PollPoller.obj

# target to build an object file
poller/PollPoller.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj
.PHONY : poller/PollPoller.cc.obj

poller/PollPoller.i: poller/PollPoller.cc.i
.PHONY : poller/PollPoller.i

# target to preprocess a source file
poller/PollPoller.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.i
.PHONY : poller/PollPoller.cc.i

poller/PollPoller.s: poller/PollPoller.cc.s
.PHONY : poller/PollPoller.s

# target to generate assembly for a file
poller/PollPoller.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.s
.PHONY : poller/PollPoller.cc.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... muduo_net
	@echo ... Acceptor.obj
	@echo ... Acceptor.i
	@echo ... Acceptor.s
	@echo ... Buffer.obj
	@echo ... Buffer.i
	@echo ... Buffer.s
	@echo ... Channel.obj
	@echo ... Channel.i
	@echo ... Channel.s
	@echo ... Connector.obj
	@echo ... Connector.i
	@echo ... Connector.s
	@echo ... EventLoop.obj
	@echo ... EventLoop.i
	@echo ... EventLoop.s
	@echo ... EventLoopThread.obj
	@echo ... EventLoopThread.i
	@echo ... EventLoopThread.s
	@echo ... EventLoopThreadPool.obj
	@echo ... EventLoopThreadPool.i
	@echo ... EventLoopThreadPool.s
	@echo ... InetAddress.obj
	@echo ... InetAddress.i
	@echo ... InetAddress.s
	@echo ... Poller.obj
	@echo ... Poller.i
	@echo ... Poller.s
	@echo ... Socket.obj
	@echo ... Socket.i
	@echo ... Socket.s
	@echo ... SocketsOps.obj
	@echo ... SocketsOps.i
	@echo ... SocketsOps.s
	@echo ... TcpClient.obj
	@echo ... TcpClient.i
	@echo ... TcpClient.s
	@echo ... TcpConnection.obj
	@echo ... TcpConnection.i
	@echo ... TcpConnection.s
	@echo ... TcpServer.obj
	@echo ... TcpServer.i
	@echo ... TcpServer.s
	@echo ... Timer.obj
	@echo ... Timer.i
	@echo ... Timer.s
	@echo ... TimerQueue.obj
	@echo ... TimerQueue.i
	@echo ... TimerQueue.s
	@echo ... poller/DefaultPoller.obj
	@echo ... poller/DefaultPoller.i
	@echo ... poller/DefaultPoller.s
	@echo ... poller/EPollPoller.obj
	@echo ... poller/EPollPoller.i
	@echo ... poller/EPollPoller.s
	@echo ... poller/PollPoller.obj
	@echo ... poller/PollPoller.i
	@echo ... poller/PollPoller.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


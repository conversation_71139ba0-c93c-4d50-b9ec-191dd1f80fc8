# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include muduo/net/http/CMakeFiles/muduo_http.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include muduo/net/http/CMakeFiles/muduo_http.dir/compiler_depend.make

# Include the progress variables for this target.
include muduo/net/http/CMakeFiles/muduo_http.dir/progress.make

# Include the compile flags for this target's objects.
include muduo/net/http/CMakeFiles/muduo_http.dir/flags.make

muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/flags.make
muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/includes_CXX.rsp
muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj: ../muduo/net/http/HttpServer.cc
muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj -MF CMakeFiles\muduo_http.dir\HttpServer.cc.obj.d -o CMakeFiles\muduo_http.dir\HttpServer.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpServer.cc

muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_http.dir/HttpServer.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpServer.cc > CMakeFiles\muduo_http.dir\HttpServer.cc.i

muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_http.dir/HttpServer.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpServer.cc -o CMakeFiles\muduo_http.dir\HttpServer.cc.s

muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/flags.make
muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/includes_CXX.rsp
muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj: ../muduo/net/http/HttpContext.cc
muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj: muduo/net/http/CMakeFiles/muduo_http.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj -MF CMakeFiles\muduo_http.dir\HttpContext.cc.obj.d -o CMakeFiles\muduo_http.dir\HttpContext.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpContext.cc

muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_http.dir/HttpContext.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpContext.cc > CMakeFiles\muduo_http.dir\HttpContext.cc.i

muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_http.dir/HttpContext.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http\HttpContext.cc -o CMakeFiles\muduo_http.dir\HttpContext.cc.s

# Object files for target muduo_http
muduo_http_OBJECTS = \
"CMakeFiles/muduo_http.dir/HttpServer.cc.obj" \
"CMakeFiles/muduo_http.dir/HttpContext.cc.obj"

# External object files for target muduo_http
muduo_http_EXTERNAL_OBJECTS =

../muduo/lib/libmuduo_http.a: muduo/net/http/CMakeFiles/muduo_http.dir/HttpServer.cc.obj
../muduo/lib/libmuduo_http.a: muduo/net/http/CMakeFiles/muduo_http.dir/HttpContext.cc.obj
../muduo/lib/libmuduo_http.a: muduo/net/http/CMakeFiles/muduo_http.dir/build.make
../muduo/lib/libmuduo_http.a: muduo/net/http/CMakeFiles/muduo_http.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library ..\..\..\..\muduo\lib\libmuduo_http.a"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && $(CMAKE_COMMAND) -P CMakeFiles\muduo_http.dir\cmake_clean_target.cmake
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\muduo_http.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
muduo/net/http/CMakeFiles/muduo_http.dir/build: ../muduo/lib/libmuduo_http.a
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/build

muduo/net/http/CMakeFiles/muduo_http.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http && $(CMAKE_COMMAND) -P CMakeFiles\muduo_http.dir\cmake_clean.cmake
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/clean

muduo/net/http/CMakeFiles/muduo_http.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\http C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\http\CMakeFiles\muduo_http.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/depend


"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f CMakeFiles\tc-client.dir/objects.a
C:\msys64\ucrt64\bin\ar.exe cr CMakeFiles\tc-client.dir/objects.a @CMakeFiles\tc-client.dir\objects1.rsp
C:\msys64\ucrt64\bin\g++.exe -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g -Wl,--whole-archive CMakeFiles\tc-client.dir/objects.a -Wl,--no-whole-archive -o ..\..\bin\tc-client.exe -Wl,--out-implib,..\..\bin\libtc-client.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\tc-client.dir\linklibs.rsp

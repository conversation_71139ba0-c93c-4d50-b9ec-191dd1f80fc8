file(REMOVE_RECURSE
  "../../../muduo/lib/libmuduo_base.a"
  "../../../muduo/lib/libmuduo_base.pdb"
  "CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj"
  "CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Condition.cc.obj"
  "CMakeFiles/muduo_base.dir/Condition.cc.obj.d"
  "CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj"
  "CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj.d"
  "CMakeFiles/muduo_base.dir/CurrentThread.cc.obj"
  "CMakeFiles/muduo_base.dir/CurrentThread.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Date.cc.obj"
  "CMakeFiles/muduo_base.dir/Date.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Exception.cc.obj"
  "CMakeFiles/muduo_base.dir/Exception.cc.obj.d"
  "CMakeFiles/muduo_base.dir/FileUtil.cc.obj"
  "CMakeFiles/muduo_base.dir/FileUtil.cc.obj.d"
  "CMakeFiles/muduo_base.dir/LogFile.cc.obj"
  "CMakeFiles/muduo_base.dir/LogFile.cc.obj.d"
  "CMakeFiles/muduo_base.dir/LogStream.cc.obj"
  "CMakeFiles/muduo_base.dir/LogStream.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Logging.cc.obj"
  "CMakeFiles/muduo_base.dir/Logging.cc.obj.d"
  "CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj"
  "CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Thread.cc.obj"
  "CMakeFiles/muduo_base.dir/Thread.cc.obj.d"
  "CMakeFiles/muduo_base.dir/ThreadPool.cc.obj"
  "CMakeFiles/muduo_base.dir/ThreadPool.cc.obj.d"
  "CMakeFiles/muduo_base.dir/TimeZone.cc.obj"
  "CMakeFiles/muduo_base.dir/TimeZone.cc.obj.d"
  "CMakeFiles/muduo_base.dir/Timestamp.cc.obj"
  "CMakeFiles/muduo_base.dir/Timestamp.cc.obj.d"
  "CMakeFiles/muduo_base.dir/md5.cc.obj"
  "CMakeFiles/muduo_base.dir/md5.cc.obj.d"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/muduo_base.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()

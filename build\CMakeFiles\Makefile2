# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: muduo/base/all
all: muduo/net/all
all: application/all
all: client/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: muduo/base/preinstall
preinstall: muduo/net/preinstall
preinstall: application/preinstall
preinstall: client/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: muduo/base/clean
clean: muduo/net/clean
clean: application/clean
clean: client/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory application

# Recursive "all" directory target.
application/all: application/tuchuang/all
.PHONY : application/all

# Recursive "preinstall" directory target.
application/preinstall: application/tuchuang/preinstall
.PHONY : application/preinstall

# Recursive "clean" directory target.
application/clean: application/tuchuang/clean
.PHONY : application/clean

#=============================================================================
# Directory level rules for directory application/tuchuang

# Recursive "all" directory target.
application/tuchuang/all: application/tuchuang/CMakeFiles/tc_http_server.dir/all
.PHONY : application/tuchuang/all

# Recursive "preinstall" directory target.
application/tuchuang/preinstall:
.PHONY : application/tuchuang/preinstall

# Recursive "clean" directory target.
application/tuchuang/clean: application/tuchuang/CMakeFiles/tc_http_server.dir/clean
.PHONY : application/tuchuang/clean

#=============================================================================
# Directory level rules for directory client

# Recursive "all" directory target.
client/all: client/1-upload/all
client/all: client/2-upload/all
.PHONY : client/all

# Recursive "preinstall" directory target.
client/preinstall: client/1-upload/preinstall
client/preinstall: client/2-upload/preinstall
.PHONY : client/preinstall

# Recursive "clean" directory target.
client/clean: client/1-upload/clean
client/clean: client/2-upload/clean
.PHONY : client/clean

#=============================================================================
# Directory level rules for directory client/1-upload

# Recursive "all" directory target.
client/1-upload/all: client/1-upload/CMakeFiles/tc-client.dir/all
client/1-upload/all: client/1-upload/CMakeFiles/tc-upload.dir/all
.PHONY : client/1-upload/all

# Recursive "preinstall" directory target.
client/1-upload/preinstall:
.PHONY : client/1-upload/preinstall

# Recursive "clean" directory target.
client/1-upload/clean: client/1-upload/CMakeFiles/tc-client.dir/clean
client/1-upload/clean: client/1-upload/CMakeFiles/tc-upload.dir/clean
.PHONY : client/1-upload/clean

#=============================================================================
# Directory level rules for directory client/2-upload

# Recursive "all" directory target.
client/2-upload/all: client/2-upload/CMakeFiles/tc-client2.dir/all
.PHONY : client/2-upload/all

# Recursive "preinstall" directory target.
client/2-upload/preinstall:
.PHONY : client/2-upload/preinstall

# Recursive "clean" directory target.
client/2-upload/clean: client/2-upload/CMakeFiles/tc-client2.dir/clean
.PHONY : client/2-upload/clean

#=============================================================================
# Directory level rules for directory muduo/base

# Recursive "all" directory target.
muduo/base/all: muduo/base/CMakeFiles/muduo_base.dir/all
.PHONY : muduo/base/all

# Recursive "preinstall" directory target.
muduo/base/preinstall:
.PHONY : muduo/base/preinstall

# Recursive "clean" directory target.
muduo/base/clean: muduo/base/CMakeFiles/muduo_base.dir/clean
.PHONY : muduo/base/clean

#=============================================================================
# Directory level rules for directory muduo/net

# Recursive "all" directory target.
muduo/net/all: muduo/net/CMakeFiles/muduo_net.dir/all
muduo/net/all: muduo/net/http/all
muduo/net/all: muduo/net/inspect/all
.PHONY : muduo/net/all

# Recursive "preinstall" directory target.
muduo/net/preinstall: muduo/net/http/preinstall
muduo/net/preinstall: muduo/net/inspect/preinstall
.PHONY : muduo/net/preinstall

# Recursive "clean" directory target.
muduo/net/clean: muduo/net/CMakeFiles/muduo_net.dir/clean
muduo/net/clean: muduo/net/http/clean
muduo/net/clean: muduo/net/inspect/clean
.PHONY : muduo/net/clean

#=============================================================================
# Directory level rules for directory muduo/net/http

# Recursive "all" directory target.
muduo/net/http/all: muduo/net/http/CMakeFiles/muduo_http.dir/all
.PHONY : muduo/net/http/all

# Recursive "preinstall" directory target.
muduo/net/http/preinstall:
.PHONY : muduo/net/http/preinstall

# Recursive "clean" directory target.
muduo/net/http/clean: muduo/net/http/CMakeFiles/muduo_http.dir/clean
.PHONY : muduo/net/http/clean

#=============================================================================
# Directory level rules for directory muduo/net/inspect

# Recursive "all" directory target.
muduo/net/inspect/all: muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all
.PHONY : muduo/net/inspect/all

# Recursive "preinstall" directory target.
muduo/net/inspect/preinstall:
.PHONY : muduo/net/inspect/preinstall

# Recursive "clean" directory target.
muduo/net/inspect/clean: muduo/net/inspect/CMakeFiles/muduo_inspect.dir/clean
.PHONY : muduo/net/inspect/clean

#=============================================================================
# Target rules for target muduo/base/CMakeFiles/muduo_base.dir

# All Build rule for target.
muduo/base/CMakeFiles/muduo_base.dir/all:
	$(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/depend
	$(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17 "Built target muduo_base"
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/all

# Build rule for subdir invocation for target.
muduo/base/CMakeFiles/muduo_base.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/CMakeFiles/muduo_base.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/rule

# Convenience name for target.
muduo_base: muduo/base/CMakeFiles/muduo_base.dir/rule
.PHONY : muduo_base

# clean rule for target.
muduo/base/CMakeFiles/muduo_base.dir/clean:
	$(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/clean
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/clean

#=============================================================================
# Target rules for target muduo/net/CMakeFiles/muduo_net.dir

# All Build rule for target.
muduo/net/CMakeFiles/muduo_net.dir/all: muduo/base/CMakeFiles/muduo_base.dir/all
	$(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/depend
	$(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45 "Built target muduo_net"
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/all

# Build rule for subdir invocation for target.
muduo/net/CMakeFiles/muduo_net.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/CMakeFiles/muduo_net.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/rule

# Convenience name for target.
muduo_net: muduo/net/CMakeFiles/muduo_net.dir/rule
.PHONY : muduo_net

# clean rule for target.
muduo/net/CMakeFiles/muduo_net.dir/clean:
	$(MAKE) $(MAKESILENT) -f muduo\net\CMakeFiles\muduo_net.dir\build.make muduo/net/CMakeFiles/muduo_net.dir/clean
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/clean

#=============================================================================
# Target rules for target muduo/net/http/CMakeFiles/muduo_http.dir

# All Build rule for target.
muduo/net/http/CMakeFiles/muduo_http.dir/all: muduo/base/CMakeFiles/muduo_base.dir/all
muduo/net/http/CMakeFiles/muduo_http.dir/all: muduo/net/CMakeFiles/muduo_net.dir/all
	$(MAKE) $(MAKESILENT) -f muduo\net\http\CMakeFiles\muduo_http.dir\build.make muduo/net/http/CMakeFiles/muduo_http.dir/depend
	$(MAKE) $(MAKESILENT) -f muduo\net\http\CMakeFiles\muduo_http.dir\build.make muduo/net/http/CMakeFiles/muduo_http.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=18,19,20 "Built target muduo_http"
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/all

# Build rule for subdir invocation for target.
muduo/net/http/CMakeFiles/muduo_http.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 40
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/http/CMakeFiles/muduo_http.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/rule

# Convenience name for target.
muduo_http: muduo/net/http/CMakeFiles/muduo_http.dir/rule
.PHONY : muduo_http

# clean rule for target.
muduo/net/http/CMakeFiles/muduo_http.dir/clean:
	$(MAKE) $(MAKESILENT) -f muduo\net\http\CMakeFiles\muduo_http.dir\build.make muduo/net/http/CMakeFiles/muduo_http.dir/clean
.PHONY : muduo/net/http/CMakeFiles/muduo_http.dir/clean

#=============================================================================
# Target rules for target muduo/net/inspect/CMakeFiles/muduo_inspect.dir

# All Build rule for target.
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all: muduo/base/CMakeFiles/muduo_base.dir/all
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all: muduo/net/http/CMakeFiles/muduo_http.dir/all
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all: muduo/net/CMakeFiles/muduo_net.dir/all
	$(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/depend
	$(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=21,22,23,24,25 "Built target muduo_inspect"
.PHONY : muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all

# Build rule for subdir invocation for target.
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 45
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/CMakeFiles/muduo_inspect.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule

# Convenience name for target.
muduo_inspect: muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule
.PHONY : muduo_inspect

# clean rule for target.
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/clean:
	$(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/clean
.PHONY : muduo/net/inspect/CMakeFiles/muduo_inspect.dir/clean

#=============================================================================
# Target rules for target application/tuchuang/CMakeFiles/tc_http_server.dir

# All Build rule for target.
application/tuchuang/CMakeFiles/tc_http_server.dir/all: muduo/base/CMakeFiles/muduo_base.dir/all
application/tuchuang/CMakeFiles/tc_http_server.dir/all: muduo/net/CMakeFiles/muduo_net.dir/all
	$(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/depend
	$(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80 "Built target tc_http_server"
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/all

# Build rule for subdir invocation for target.
application/tuchuang/CMakeFiles/tc_http_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 66
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/CMakeFiles/tc_http_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/rule

# Convenience name for target.
tc_http_server: application/tuchuang/CMakeFiles/tc_http_server.dir/rule
.PHONY : tc_http_server

# clean rule for target.
application/tuchuang/CMakeFiles/tc_http_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/clean
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/clean

#=============================================================================
# Target rules for target client/1-upload/CMakeFiles/tc-client.dir

# All Build rule for target.
client/1-upload/CMakeFiles/tc-client.dir/all:
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-client.dir\build.make client/1-upload/CMakeFiles/tc-client.dir/depend
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-client.dir\build.make client/1-upload/CMakeFiles/tc-client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=46,47 "Built target tc-client"
.PHONY : client/1-upload/CMakeFiles/tc-client.dir/all

# Build rule for subdir invocation for target.
client/1-upload/CMakeFiles/tc-client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 client/1-upload/CMakeFiles/tc-client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : client/1-upload/CMakeFiles/tc-client.dir/rule

# Convenience name for target.
tc-client: client/1-upload/CMakeFiles/tc-client.dir/rule
.PHONY : tc-client

# clean rule for target.
client/1-upload/CMakeFiles/tc-client.dir/clean:
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-client.dir\build.make client/1-upload/CMakeFiles/tc-client.dir/clean
.PHONY : client/1-upload/CMakeFiles/tc-client.dir/clean

#=============================================================================
# Target rules for target client/1-upload/CMakeFiles/tc-upload.dir

# All Build rule for target.
client/1-upload/CMakeFiles/tc-upload.dir/all:
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-upload.dir\build.make client/1-upload/CMakeFiles/tc-upload.dir/depend
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-upload.dir\build.make client/1-upload/CMakeFiles/tc-upload.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=50,51 "Built target tc-upload"
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/all

# Build rule for subdir invocation for target.
client/1-upload/CMakeFiles/tc-upload.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 client/1-upload/CMakeFiles/tc-upload.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/rule

# Convenience name for target.
tc-upload: client/1-upload/CMakeFiles/tc-upload.dir/rule
.PHONY : tc-upload

# clean rule for target.
client/1-upload/CMakeFiles/tc-upload.dir/clean:
	$(MAKE) $(MAKESILENT) -f client\1-upload\CMakeFiles\tc-upload.dir\build.make client/1-upload/CMakeFiles/tc-upload.dir/clean
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/clean

#=============================================================================
# Target rules for target client/2-upload/CMakeFiles/tc-client2.dir

# All Build rule for target.
client/2-upload/CMakeFiles/tc-client2.dir/all: muduo/base/CMakeFiles/muduo_base.dir/all
client/2-upload/CMakeFiles/tc-client2.dir/all: muduo/net/CMakeFiles/muduo_net.dir/all
	$(MAKE) $(MAKESILENT) -f client\2-upload\CMakeFiles\tc-client2.dir\build.make client/2-upload/CMakeFiles/tc-client2.dir/depend
	$(MAKE) $(MAKESILENT) -f client\2-upload\CMakeFiles\tc-client2.dir\build.make client/2-upload/CMakeFiles/tc-client2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=48,49 "Built target tc-client2"
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/all

# Build rule for subdir invocation for target.
client/2-upload/CMakeFiles/tc-client2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 39
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 client/2-upload/CMakeFiles/tc-client2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/rule

# Convenience name for target.
tc-client2: client/2-upload/CMakeFiles/tc-client2.dir/rule
.PHONY : tc-client2

# clean rule for target.
client/2-upload/CMakeFiles/tc-client2.dir/clean:
	$(MAKE) $(MAKESILENT) -f client\2-upload\CMakeFiles\tc-client2.dir\build.make client/2-upload/CMakeFiles/tc-client2.dir/clean
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


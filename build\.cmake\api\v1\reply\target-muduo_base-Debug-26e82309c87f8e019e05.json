{"archive": {}, "artifacts": [{"path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/lib/libmuduo_base.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "include_directories"], "files": ["muduo/base/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"command": 1, "file": 0, "line": 27, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 71, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "id": "muduo_base::@af02cc837cde80c48ca0", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/tuchuang"}}, "name": "muduo_base", "nameOnDisk": "libmuduo_base.a", "paths": {"build": "muduo/base", "source": "muduo/base"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/AsyncLogging.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Condition.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/CountDownLatch.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/CurrentThread.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Date.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Exception.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/FileUtil.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/LogFile.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Logging.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/LogStream.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/ProcessInfo.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Timestamp.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/Thread.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/ThreadPool.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/TimeZone.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "muduo/base/md5.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
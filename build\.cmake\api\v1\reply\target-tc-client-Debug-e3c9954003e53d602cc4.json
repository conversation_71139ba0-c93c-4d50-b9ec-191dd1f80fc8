{"artifacts": [{"path": "bin/tc-client.exe"}, {"path": "bin/tc-client.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["client/1-upload/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 71, "parent": 2}, {"command": 2, "file": 0, "line": 2, "parent": 0}, {"command": 2, "file": 0, "line": 4, "parent": 0}, {"command": 2, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}, {"backtrace": 4, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src.."}, {"backtrace": 5, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/client"}, {"backtrace": 6, "path": "/usr/include/jsoncpp"}], "language": "CXX", "sourceIndexes": [0]}], "id": "tc-client::@214c07589b956b7e49bc", "link": {"commandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "tc-client", "nameOnDisk": "tc-client.exe", "paths": {"build": "client/1-upload", "source": "client/1-upload"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "client/1-upload/tc-client.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}
# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include application/tuchuang/CMakeFiles/tc_http_server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.make

# Include the progress variables for this target.
include application/tuchuang/CMakeFiles/tc_http_server.dir/progress.make

# Include the compile flags for this target's objects.
include application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make

application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj: ../application/tuchuang/main.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj -MF CMakeFiles\tc_http_server.dir\main.cc.obj.d -o CMakeFiles\tc_http_server.dir\main.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\main.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/main.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\main.cc > CMakeFiles\tc_http_server.dir\main.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/main.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\main.cc -o CMakeFiles\tc_http_server.dir\main.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj: ../application/tuchuang/http_parser.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj -MF CMakeFiles\tc_http_server.dir\http_parser.cc.obj.d -o CMakeFiles\tc_http_server.dir\http_parser.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/http_parser.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser.cc > CMakeFiles\tc_http_server.dir\http_parser.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/http_parser.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser.cc -o CMakeFiles\tc_http_server.dir\http_parser.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj: ../application/tuchuang/http_parser_wrapper.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj -MF CMakeFiles\tc_http_server.dir\http_parser_wrapper.cc.obj.d -o CMakeFiles\tc_http_server.dir\http_parser_wrapper.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser_wrapper.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser_wrapper.cc > CMakeFiles\tc_http_server.dir\http_parser_wrapper.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_parser_wrapper.cc -o CMakeFiles\tc_http_server.dir\http_parser_wrapper.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj: ../application/tuchuang/http_conn.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj -MF CMakeFiles\tc_http_server.dir\http_conn.cc.obj.d -o CMakeFiles\tc_http_server.dir\http_conn.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_conn.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/http_conn.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_conn.cc > CMakeFiles\tc_http_server.dir\http_conn.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/http_conn.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\http_conn.cc -o CMakeFiles\tc_http_server.dir\http_conn.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj: ../application/tuchuang/base/config_file_reader.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj -MF CMakeFiles\tc_http_server.dir\base\config_file_reader.cc.obj.d -o CMakeFiles\tc_http_server.dir\base\config_file_reader.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\config_file_reader.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\config_file_reader.cc > CMakeFiles\tc_http_server.dir\base\config_file_reader.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\config_file_reader.cc -o CMakeFiles\tc_http_server.dir\base\config_file_reader.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj: ../application/tuchuang/base/tc_common.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj -MF CMakeFiles\tc_http_server.dir\base\tc_common.cc.obj.d -o CMakeFiles\tc_http_server.dir\base\tc_common.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\tc_common.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/base/tc_common.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\tc_common.cc > CMakeFiles\tc_http_server.dir\base\tc_common.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/base/tc_common.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\tc_common.cc -o CMakeFiles\tc_http_server.dir\base\tc_common.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj: ../application/tuchuang/base/util.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj -MF CMakeFiles\tc_http_server.dir\base\util.cc.obj.d -o CMakeFiles\tc_http_server.dir\base\util.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\util.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/base/util.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\util.cc > CMakeFiles\tc_http_server.dir\base\util.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/base/util.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\base\util.cc -o CMakeFiles\tc_http_server.dir\base\util.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj: ../application/tuchuang/api/api_common.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_common.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_common.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_common.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_common.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_common.cc > CMakeFiles\tc_http_server.dir\api\api_common.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_common.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_common.cc -o CMakeFiles\tc_http_server.dir\api\api_common.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj: ../application/tuchuang/api/api_deal_sharefile.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_deal_sharefile.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_deal_sharefile.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_deal_sharefile.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_deal_sharefile.cc > CMakeFiles\tc_http_server.dir\api\api_deal_sharefile.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_deal_sharefile.cc -o CMakeFiles\tc_http_server.dir\api\api_deal_sharefile.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj: ../application/tuchuang/api/api_dealfile.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_dealfile.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_dealfile.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_dealfile.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_dealfile.cc > CMakeFiles\tc_http_server.dir\api\api_dealfile.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_dealfile.cc -o CMakeFiles\tc_http_server.dir\api\api_dealfile.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj: ../application/tuchuang/api/api_login.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_login.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_login.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_login.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_login.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_login.cc > CMakeFiles\tc_http_server.dir\api\api_login.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_login.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_login.cc -o CMakeFiles\tc_http_server.dir\api\api_login.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj: ../application/tuchuang/api/api_md5.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_md5.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_md5.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_md5.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_md5.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_md5.cc > CMakeFiles\tc_http_server.dir\api\api_md5.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_md5.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_md5.cc -o CMakeFiles\tc_http_server.dir\api\api_md5.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj: ../application/tuchuang/api/api_myfiles.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_myfiles.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_myfiles.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_myfiles.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_myfiles.cc > CMakeFiles\tc_http_server.dir\api\api_myfiles.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_myfiles.cc -o CMakeFiles\tc_http_server.dir\api\api_myfiles.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj: ../application/tuchuang/api/api_register.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_register.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_register.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_register.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_register.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_register.cc > CMakeFiles\tc_http_server.dir\api\api_register.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_register.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_register.cc -o CMakeFiles\tc_http_server.dir\api\api_register.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj: ../application/tuchuang/api/api_sharefiles.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_sharefiles.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_sharefiles.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharefiles.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharefiles.cc > CMakeFiles\tc_http_server.dir\api\api_sharefiles.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharefiles.cc -o CMakeFiles\tc_http_server.dir\api\api_sharefiles.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj: ../application/tuchuang/api/api_sharepicture.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_sharepicture.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_sharepicture.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharepicture.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharepicture.cc > CMakeFiles\tc_http_server.dir\api\api_sharepicture.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_sharepicture.cc -o CMakeFiles\tc_http_server.dir\api\api_sharepicture.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj: ../application/tuchuang/api/api_upload.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj -MF CMakeFiles\tc_http_server.dir\api\api_upload.cc.obj.d -o CMakeFiles\tc_http_server.dir\api\api_upload.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_upload.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/api/api_upload.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_upload.cc > CMakeFiles\tc_http_server.dir\api\api_upload.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/api/api_upload.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\api\api_upload.cc -o CMakeFiles\tc_http_server.dir\api\api_upload.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj: ../application/tuchuang/mysql/db_pool.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj -MF CMakeFiles\tc_http_server.dir\mysql\db_pool.cc.obj.d -o CMakeFiles\tc_http_server.dir\mysql\db_pool.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\mysql\db_pool.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\mysql\db_pool.cc > CMakeFiles\tc_http_server.dir\mysql\db_pool.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\mysql\db_pool.cc -o CMakeFiles\tc_http_server.dir\mysql\db_pool.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj: ../application/tuchuang/redis/async.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj -MF CMakeFiles\tc_http_server.dir\redis\async.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\async.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\async.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/async.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\async.c > CMakeFiles\tc_http_server.dir\redis\async.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/async.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\async.c -o CMakeFiles\tc_http_server.dir\redis\async.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj: ../application/tuchuang/redis/cache_pool.cc
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj -MF CMakeFiles\tc_http_server.dir\redis\cache_pool.cc.obj.d -o CMakeFiles\tc_http_server.dir\redis\cache_pool.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\cache_pool.cc

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\cache_pool.cc > CMakeFiles\tc_http_server.dir\redis\cache_pool.cc.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\cache_pool.cc -o CMakeFiles\tc_http_server.dir\redis\cache_pool.cc.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj: ../application/tuchuang/redis/dict.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj -MF CMakeFiles\tc_http_server.dir\redis\dict.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\dict.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\dict.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/dict.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\dict.c > CMakeFiles\tc_http_server.dir\redis\dict.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/dict.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\dict.c -o CMakeFiles\tc_http_server.dir\redis\dict.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj: ../application/tuchuang/redis/hiredis.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj -MF CMakeFiles\tc_http_server.dir\redis\hiredis.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\hiredis.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\hiredis.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/hiredis.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\hiredis.c > CMakeFiles\tc_http_server.dir\redis\hiredis.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/hiredis.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\hiredis.c -o CMakeFiles\tc_http_server.dir\redis\hiredis.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj: ../application/tuchuang/redis/net.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj -MF CMakeFiles\tc_http_server.dir\redis\net.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\net.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\net.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/net.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\net.c > CMakeFiles\tc_http_server.dir\redis\net.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/net.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\net.c -o CMakeFiles\tc_http_server.dir\redis\net.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj: ../application/tuchuang/redis/read.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj -MF CMakeFiles\tc_http_server.dir\redis\read.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\read.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\read.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/read.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\read.c > CMakeFiles\tc_http_server.dir\redis\read.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/read.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\read.c -o CMakeFiles\tc_http_server.dir\redis\read.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_C.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj: ../application/tuchuang/redis/sds.c
application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj -MF CMakeFiles\tc_http_server.dir\redis\sds.c.obj.d -o CMakeFiles\tc_http_server.dir\redis\sds.c.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\sds.c

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/tc_http_server.dir/redis/sds.c.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\sds.c > CMakeFiles\tc_http_server.dir\redis\sds.c.i

application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/tc_http_server.dir/redis/sds.c.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\redis\sds.c -o CMakeFiles\tc_http_server.dir\redis\sds.c.s

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj: ../application/tuchuang/jsoncpp/json_reader.cpp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj -MF CMakeFiles\tc_http_server.dir\jsoncpp\json_reader.cpp.obj.d -o CMakeFiles\tc_http_server.dir\jsoncpp\json_reader.cpp.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_reader.cpp

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_reader.cpp > CMakeFiles\tc_http_server.dir\jsoncpp\json_reader.cpp.i

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_reader.cpp -o CMakeFiles\tc_http_server.dir\jsoncpp\json_reader.cpp.s

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj: ../application/tuchuang/jsoncpp/json_value.cpp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj -MF CMakeFiles\tc_http_server.dir\jsoncpp\json_value.cpp.obj.d -o CMakeFiles\tc_http_server.dir\jsoncpp\json_value.cpp.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_value.cpp

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_value.cpp > CMakeFiles\tc_http_server.dir\jsoncpp\json_value.cpp.i

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_value.cpp -o CMakeFiles\tc_http_server.dir\jsoncpp\json_value.cpp.s

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/flags.make
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/includes_CXX.rsp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj: ../application/tuchuang/jsoncpp/json_writer.cpp
application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj: application/tuchuang/CMakeFiles/tc_http_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj -MF CMakeFiles\tc_http_server.dir\jsoncpp\json_writer.cpp.obj.d -o CMakeFiles\tc_http_server.dir\jsoncpp\json_writer.cpp.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_writer.cpp

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_writer.cpp > CMakeFiles\tc_http_server.dir\jsoncpp\json_writer.cpp.i

application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang\jsoncpp\json_writer.cpp -o CMakeFiles\tc_http_server.dir\jsoncpp\json_writer.cpp.s

# Object files for target tc_http_server
tc_http_server_OBJECTS = \
"CMakeFiles/tc_http_server.dir/main.cc.obj" \
"CMakeFiles/tc_http_server.dir/http_parser.cc.obj" \
"CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj" \
"CMakeFiles/tc_http_server.dir/http_conn.cc.obj" \
"CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj" \
"CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj" \
"CMakeFiles/tc_http_server.dir/base/util.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_common.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_login.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_register.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj" \
"CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj" \
"CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj" \
"CMakeFiles/tc_http_server.dir/redis/async.c.obj" \
"CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj" \
"CMakeFiles/tc_http_server.dir/redis/dict.c.obj" \
"CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj" \
"CMakeFiles/tc_http_server.dir/redis/net.c.obj" \
"CMakeFiles/tc_http_server.dir/redis/read.c.obj" \
"CMakeFiles/tc_http_server.dir/redis/sds.c.obj" \
"CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj" \
"CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj" \
"CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj"

# External object files for target tc_http_server
tc_http_server_EXTERNAL_OBJECTS =

bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/build.make
bin/tc_http_server.exe: ../muduo/lib/libmuduo_net.a
bin/tc_http_server.exe: ../muduo/lib/libmuduo_base.a
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/linklibs.rsp
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/objects1.rsp
bin/tc_http_server.exe: application/tuchuang/CMakeFiles/tc_http_server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Linking CXX executable ..\..\bin\tc_http_server.exe"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\tc_http_server.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
application/tuchuang/CMakeFiles/tc_http_server.dir/build: bin/tc_http_server.exe
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/build

application/tuchuang/CMakeFiles/tc_http_server.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang && $(CMAKE_COMMAND) -P CMakeFiles\tc_http_server.dir\cmake_clean.cmake
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/clean

application/tuchuang/CMakeFiles/tc_http_server.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\application\tuchuang C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang\CMakeFiles\tc_http_server.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/depend


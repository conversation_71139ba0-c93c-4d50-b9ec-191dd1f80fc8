# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeCInformation.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeCXXInformation.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeGenericSystem.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeInitializeConfigs.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeRCInformation.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/CheckFunctionExists.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Compiler/GNU-C.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Compiler/GNU-CXX.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Compiler/GNU.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-GNU-C.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-GNU.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows-windres.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/Windows.cmake"
  "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20/Modules/Platform/WindowsPaths.cmake"
  "../CMakeLists.txt"
  "../application/CMakeLists.txt"
  "../application/tuchuang/CMakeLists.txt"
  "CMakeFiles/3.20.21032501-MSVC_2/CMakeCCompiler.cmake"
  "CMakeFiles/3.20.21032501-MSVC_2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.20.21032501-MSVC_2/CMakeRCCompiler.cmake"
  "CMakeFiles/3.20.21032501-MSVC_2/CMakeSystem.cmake"
  "../client/1-upload/CMakeLists.txt"
  "../client/2-upload/CMakeLists.txt"
  "../client/CMakeLists.txt"
  "../muduo/base/CMakeLists.txt"
  "../muduo/net/CMakeLists.txt"
  "../muduo/net/http/CMakeLists.txt"
  "../muduo/net/inspect/CMakeLists.txt"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "muduo/base/CMakeFiles/CMakeDirectoryInformation.cmake"
  "muduo/net/CMakeFiles/CMakeDirectoryInformation.cmake"
  "muduo/net/http/CMakeFiles/CMakeDirectoryInformation.cmake"
  "muduo/net/inspect/CMakeFiles/CMakeDirectoryInformation.cmake"
  "application/CMakeFiles/CMakeDirectoryInformation.cmake"
  "application/tuchuang/CMakeFiles/CMakeDirectoryInformation.cmake"
  "client/CMakeFiles/CMakeDirectoryInformation.cmake"
  "client/1-upload/CMakeFiles/CMakeDirectoryInformation.cmake"
  "client/2-upload/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "muduo/base/CMakeFiles/muduo_base.dir/DependInfo.cmake"
  "muduo/net/CMakeFiles/muduo_net.dir/DependInfo.cmake"
  "muduo/net/http/CMakeFiles/muduo_http.dir/DependInfo.cmake"
  "muduo/net/inspect/CMakeFiles/muduo_inspect.dir/DependInfo.cmake"
  "application/tuchuang/CMakeFiles/tc_http_server.dir/DependInfo.cmake"
  "client/1-upload/CMakeFiles/tc-client.dir/DependInfo.cmake"
  "client/1-upload/CMakeFiles/tc-upload.dir/DependInfo.cmake"
  "client/2-upload/CMakeFiles/tc-client2.dir/DependInfo.cmake"
  )

# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include muduo/net/CMakeFiles/muduo_net.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.make

# Include the progress variables for this target.
include muduo/net/CMakeFiles/muduo_net.dir/progress.make

# Include the compile flags for this target's objects.
include muduo/net/CMakeFiles/muduo_net.dir/flags.make

muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj: ../muduo/net/Acceptor.cc
muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj -MF CMakeFiles\muduo_net.dir\Acceptor.cc.obj.d -o CMakeFiles\muduo_net.dir\Acceptor.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Acceptor.cc

muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Acceptor.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Acceptor.cc > CMakeFiles\muduo_net.dir\Acceptor.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Acceptor.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Acceptor.cc -o CMakeFiles\muduo_net.dir\Acceptor.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj: ../muduo/net/Buffer.cc
muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj -MF CMakeFiles\muduo_net.dir\Buffer.cc.obj.d -o CMakeFiles\muduo_net.dir\Buffer.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Buffer.cc

muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Buffer.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Buffer.cc > CMakeFiles\muduo_net.dir\Buffer.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Buffer.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Buffer.cc -o CMakeFiles\muduo_net.dir\Buffer.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj: ../muduo/net/Channel.cc
muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj -MF CMakeFiles\muduo_net.dir\Channel.cc.obj.d -o CMakeFiles\muduo_net.dir\Channel.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Channel.cc

muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Channel.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Channel.cc > CMakeFiles\muduo_net.dir\Channel.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Channel.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Channel.cc -o CMakeFiles\muduo_net.dir\Channel.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj: ../muduo/net/Connector.cc
muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj -MF CMakeFiles\muduo_net.dir\Connector.cc.obj.d -o CMakeFiles\muduo_net.dir\Connector.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Connector.cc

muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Connector.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Connector.cc > CMakeFiles\muduo_net.dir\Connector.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Connector.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Connector.cc -o CMakeFiles\muduo_net.dir\Connector.cc.s

muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj: ../muduo/net/EventLoop.cc
muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj -MF CMakeFiles\muduo_net.dir\EventLoop.cc.obj.d -o CMakeFiles\muduo_net.dir\EventLoop.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoop.cc

muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/EventLoop.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoop.cc > CMakeFiles\muduo_net.dir\EventLoop.cc.i

muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/EventLoop.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoop.cc -o CMakeFiles\muduo_net.dir\EventLoop.cc.s

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj: ../muduo/net/EventLoopThread.cc
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj -MF CMakeFiles\muduo_net.dir\EventLoopThread.cc.obj.d -o CMakeFiles\muduo_net.dir\EventLoopThread.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThread.cc

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/EventLoopThread.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThread.cc > CMakeFiles\muduo_net.dir\EventLoopThread.cc.i

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/EventLoopThread.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThread.cc -o CMakeFiles\muduo_net.dir\EventLoopThread.cc.s

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj: ../muduo/net/EventLoopThreadPool.cc
muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj -MF CMakeFiles\muduo_net.dir\EventLoopThreadPool.cc.obj.d -o CMakeFiles\muduo_net.dir\EventLoopThreadPool.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThreadPool.cc

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThreadPool.cc > CMakeFiles\muduo_net.dir\EventLoopThreadPool.cc.i

muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\EventLoopThreadPool.cc -o CMakeFiles\muduo_net.dir\EventLoopThreadPool.cc.s

muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj: ../muduo/net/InetAddress.cc
muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj -MF CMakeFiles\muduo_net.dir\InetAddress.cc.obj.d -o CMakeFiles\muduo_net.dir\InetAddress.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\InetAddress.cc

muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/InetAddress.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\InetAddress.cc > CMakeFiles\muduo_net.dir\InetAddress.cc.i

muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/InetAddress.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\InetAddress.cc -o CMakeFiles\muduo_net.dir\InetAddress.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj: ../muduo/net/Poller.cc
muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj -MF CMakeFiles\muduo_net.dir\Poller.cc.obj.d -o CMakeFiles\muduo_net.dir\Poller.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Poller.cc

muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Poller.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Poller.cc > CMakeFiles\muduo_net.dir\Poller.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Poller.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Poller.cc -o CMakeFiles\muduo_net.dir\Poller.cc.s

muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj: ../muduo/net/poller/DefaultPoller.cc
muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj -MF CMakeFiles\muduo_net.dir\poller\DefaultPoller.cc.obj.d -o CMakeFiles\muduo_net.dir\poller\DefaultPoller.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\DefaultPoller.cc

muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\DefaultPoller.cc > CMakeFiles\muduo_net.dir\poller\DefaultPoller.cc.i

muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\DefaultPoller.cc -o CMakeFiles\muduo_net.dir\poller\DefaultPoller.cc.s

muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj: ../muduo/net/poller/EPollPoller.cc
muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj -MF CMakeFiles\muduo_net.dir\poller\EPollPoller.cc.obj.d -o CMakeFiles\muduo_net.dir\poller\EPollPoller.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\EPollPoller.cc

muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\EPollPoller.cc > CMakeFiles\muduo_net.dir\poller\EPollPoller.cc.i

muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\EPollPoller.cc -o CMakeFiles\muduo_net.dir\poller\EPollPoller.cc.s

muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj: ../muduo/net/poller/PollPoller.cc
muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj -MF CMakeFiles\muduo_net.dir\poller\PollPoller.cc.obj.d -o CMakeFiles\muduo_net.dir\poller\PollPoller.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\PollPoller.cc

muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/poller/PollPoller.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\PollPoller.cc > CMakeFiles\muduo_net.dir\poller\PollPoller.cc.i

muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/poller/PollPoller.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\poller\PollPoller.cc -o CMakeFiles\muduo_net.dir\poller\PollPoller.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj: ../muduo/net/Socket.cc
muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj -MF CMakeFiles\muduo_net.dir\Socket.cc.obj.d -o CMakeFiles\muduo_net.dir\Socket.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Socket.cc

muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Socket.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Socket.cc > CMakeFiles\muduo_net.dir\Socket.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Socket.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Socket.cc -o CMakeFiles\muduo_net.dir\Socket.cc.s

muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj: ../muduo/net/SocketsOps.cc
muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -DNO_ACCEPT4 -MD -MT muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj -MF CMakeFiles\muduo_net.dir\SocketsOps.cc.obj.d -o CMakeFiles\muduo_net.dir\SocketsOps.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\SocketsOps.cc

muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/SocketsOps.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -DNO_ACCEPT4 -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\SocketsOps.cc > CMakeFiles\muduo_net.dir\SocketsOps.cc.i

muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/SocketsOps.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -DNO_ACCEPT4 -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\SocketsOps.cc -o CMakeFiles\muduo_net.dir\SocketsOps.cc.s

muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj: ../muduo/net/TcpClient.cc
muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj -MF CMakeFiles\muduo_net.dir\TcpClient.cc.obj.d -o CMakeFiles\muduo_net.dir\TcpClient.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpClient.cc

muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/TcpClient.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpClient.cc > CMakeFiles\muduo_net.dir\TcpClient.cc.i

muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/TcpClient.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpClient.cc -o CMakeFiles\muduo_net.dir\TcpClient.cc.s

muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj: ../muduo/net/TcpConnection.cc
muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj -MF CMakeFiles\muduo_net.dir\TcpConnection.cc.obj.d -o CMakeFiles\muduo_net.dir\TcpConnection.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpConnection.cc

muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/TcpConnection.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpConnection.cc > CMakeFiles\muduo_net.dir\TcpConnection.cc.i

muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/TcpConnection.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpConnection.cc -o CMakeFiles\muduo_net.dir\TcpConnection.cc.s

muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj: ../muduo/net/TcpServer.cc
muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj -MF CMakeFiles\muduo_net.dir\TcpServer.cc.obj.d -o CMakeFiles\muduo_net.dir\TcpServer.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpServer.cc

muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/TcpServer.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpServer.cc > CMakeFiles\muduo_net.dir\TcpServer.cc.i

muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/TcpServer.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TcpServer.cc -o CMakeFiles\muduo_net.dir\TcpServer.cc.s

muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj: ../muduo/net/Timer.cc
muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj -MF CMakeFiles\muduo_net.dir\Timer.cc.obj.d -o CMakeFiles\muduo_net.dir\Timer.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Timer.cc

muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/Timer.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Timer.cc > CMakeFiles\muduo_net.dir\Timer.cc.i

muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/Timer.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\Timer.cc -o CMakeFiles\muduo_net.dir\Timer.cc.s

muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/flags.make
muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/includes_CXX.rsp
muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj: ../muduo/net/TimerQueue.cc
muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj: muduo/net/CMakeFiles/muduo_net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj -MF CMakeFiles\muduo_net.dir\TimerQueue.cc.obj.d -o CMakeFiles\muduo_net.dir\TimerQueue.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TimerQueue.cc

muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/muduo_net.dir/TimerQueue.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TimerQueue.cc > CMakeFiles\muduo_net.dir\TimerQueue.cc.i

muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/muduo_net.dir/TimerQueue.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net\TimerQueue.cc -o CMakeFiles\muduo_net.dir\TimerQueue.cc.s

# Object files for target muduo_net
muduo_net_OBJECTS = \
"CMakeFiles/muduo_net.dir/Acceptor.cc.obj" \
"CMakeFiles/muduo_net.dir/Buffer.cc.obj" \
"CMakeFiles/muduo_net.dir/Channel.cc.obj" \
"CMakeFiles/muduo_net.dir/Connector.cc.obj" \
"CMakeFiles/muduo_net.dir/EventLoop.cc.obj" \
"CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj" \
"CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj" \
"CMakeFiles/muduo_net.dir/InetAddress.cc.obj" \
"CMakeFiles/muduo_net.dir/Poller.cc.obj" \
"CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj" \
"CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj" \
"CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj" \
"CMakeFiles/muduo_net.dir/Socket.cc.obj" \
"CMakeFiles/muduo_net.dir/SocketsOps.cc.obj" \
"CMakeFiles/muduo_net.dir/TcpClient.cc.obj" \
"CMakeFiles/muduo_net.dir/TcpConnection.cc.obj" \
"CMakeFiles/muduo_net.dir/TcpServer.cc.obj" \
"CMakeFiles/muduo_net.dir/Timer.cc.obj" \
"CMakeFiles/muduo_net.dir/TimerQueue.cc.obj"

# External object files for target muduo_net
muduo_net_EXTERNAL_OBJECTS =

../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Acceptor.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Buffer.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Channel.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Connector.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/EventLoop.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/EventLoopThread.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/EventLoopThreadPool.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/InetAddress.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Poller.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/poller/DefaultPoller.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/poller/EPollPoller.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/poller/PollPoller.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Socket.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/SocketsOps.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/TcpClient.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/TcpConnection.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/TcpServer.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/Timer.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/TimerQueue.cc.obj
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/build.make
../muduo/lib/libmuduo_net.a: muduo/net/CMakeFiles/muduo_net.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Linking CXX static library ..\..\..\muduo\lib\libmuduo_net.a"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && $(CMAKE_COMMAND) -P CMakeFiles\muduo_net.dir\cmake_clean_target.cmake
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\muduo_net.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
muduo/net/CMakeFiles/muduo_net.dir/build: ../muduo/lib/libmuduo_net.a
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/build

muduo/net/CMakeFiles/muduo_net.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net && $(CMAKE_COMMAND) -P CMakeFiles\muduo_net.dir\cmake_clean.cmake
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/clean

muduo/net/CMakeFiles/muduo_net.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\muduo\net C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\CMakeFiles\muduo_net.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : muduo/net/CMakeFiles/muduo_net.dir/depend


# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include client/2-upload/CMakeFiles/tc-client2.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include client/2-upload/CMakeFiles/tc-client2.dir/compiler_depend.make

# Include the progress variables for this target.
include client/2-upload/CMakeFiles/tc-client2.dir/progress.make

# Include the compile flags for this target's objects.
include client/2-upload/CMakeFiles/tc-client2.dir/flags.make

client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj: client/2-upload/CMakeFiles/tc-client2.dir/flags.make
client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj: client/2-upload/CMakeFiles/tc-client2.dir/includes_CXX.rsp
client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj: ../client/2-upload/tc-client.cc
client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj: client/2-upload/CMakeFiles/tc-client2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj -MF CMakeFiles\tc-client2.dir\tc-client.cc.obj.d -o CMakeFiles\tc-client2.dir\tc-client.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\2-upload\tc-client.cc

client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc-client2.dir/tc-client.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\2-upload\tc-client.cc > CMakeFiles\tc-client2.dir\tc-client.cc.i

client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc-client2.dir/tc-client.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\2-upload\tc-client.cc -o CMakeFiles\tc-client2.dir\tc-client.cc.s

# Object files for target tc-client2
tc__client2_OBJECTS = \
"CMakeFiles/tc-client2.dir/tc-client.cc.obj"

# External object files for target tc-client2
tc__client2_EXTERNAL_OBJECTS =

bin/tc-client2.exe: client/2-upload/CMakeFiles/tc-client2.dir/tc-client.cc.obj
bin/tc-client2.exe: client/2-upload/CMakeFiles/tc-client2.dir/build.make
bin/tc-client2.exe: ../muduo/lib/libmuduo_net.a
bin/tc-client2.exe: ../muduo/lib/libmuduo_base.a
bin/tc-client2.exe: client/2-upload/CMakeFiles/tc-client2.dir/linklibs.rsp
bin/tc-client2.exe: client/2-upload/CMakeFiles/tc-client2.dir/objects1.rsp
bin/tc-client2.exe: client/2-upload/CMakeFiles/tc-client2.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ..\..\bin\tc-client2.exe"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\tc-client2.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
client/2-upload/CMakeFiles/tc-client2.dir/build: bin/tc-client2.exe
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/build

client/2-upload/CMakeFiles/tc-client2.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload && $(CMAKE_COMMAND) -P CMakeFiles\tc-client2.dir\cmake_clean.cmake
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/clean

client/2-upload/CMakeFiles/tc-client2.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\2-upload C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\2-upload\CMakeFiles\tc-client2.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : client/2-upload/CMakeFiles/tc-client2.dir/depend


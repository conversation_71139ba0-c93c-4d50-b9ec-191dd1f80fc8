# Install script for directory: C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/tuchuang")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "C:/msys64/ucrt64/bin/objdump.exe")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/lib/libmuduo_base.a")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/muduo/base" TYPE FILE FILES
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/AsyncLogging.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Atomic.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/BlockingQueue.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/BoundedBlockingQueue.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Condition.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/CountDownLatch.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/CurrentThread.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Date.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Exception.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/FileUtil.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/GzipFile.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/LogFile.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/LogStream.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Logging.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Mutex.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ProcessInfo.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Singleton.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/StringPiece.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Thread.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ThreadLocal.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ThreadLocalSingleton.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ThreadPool.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/TimeZone.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Timestamp.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Types.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/WeakCallback.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/copyable.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/md5.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/noncopyable.h"
    )
endif()


# config format spec
# 绑定的ip和端口
http_bind_ip=0.0.0.0
http_bind_port=8081

# io线程数量 默认先用单个epoll
num_event_loops=4
# 业务线程数量
num_threads=64
# epoll 超时时间
timeout_ms=10
# nodelay参数 目前不影响性能
nodelay=1

# 测试性能的时候改为WARN级别,默认INFO
#   TRACE = 0, // 0
#   DEBUG,      //1
#   INFO,       //2
#   WARN,       //3
#   ERROR,      //4
#   FATAL,      //5
log_level=4

# 是否开启短链, 主要是图片分享地址，如果开启需要设置shorturl-server grpc服务地址
# 课程前期先禁用短链,避免一上来就把课程搞复杂
enable_shorturl=0
# 因为当前部署是同一台机器所以使用127.0.0.1，注意端口和shorturl-server保持一致
shorturl_server_address=127.0.0.1:50051
shorturl_server_access_token=e8n05nr9jey84prEhw5u43th0yi294780yjr3h7sksSdkFdDngKi

dfs_path_client=/etc/fdfs/client.conf
storage_web_server_ip=************
storage_web_server_port=80

#configure for mysql
DBInstances=tuchuang_master,tuchuang_slave
#tuchuang_master
tuchuang_master_host=localhost
tuchuang_master_port=3306
tuchuang_master_dbname=0voice_tuchuang
tuchuang_master_username=root
tuchuang_master_password=123456
tuchuang_master_maxconncnt=128

#tuchuang_slave
tuchuang_slave_host=localhost
tuchuang_slave_port=3306
tuchuang_slave_dbname=0voice_tuchuang
tuchuang_slave_username=root
tuchuang_slave_password=123456
tuchuang_slave_maxconncnt=128


#configure for token
CacheInstances=token,ranking_list
#token相关
token_host=127.0.0.1
token_port=6379
token_db=0
token_maxconncnt=128

# 排行榜相关，但目前排行也是直接用了token的连接池
ranking_list_host=127.0.0.1
ranking_list_port=6379
ranking_list_db=1
ranking_list_maxconncnt=128
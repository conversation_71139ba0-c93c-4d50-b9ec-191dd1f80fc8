{"artifacts": [{"path": "bin/tc_http_server.exe"}, {"path": "bin/tc_http_server.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "TARGET_LINK_LIBRARIES", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["application/tuchuang/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 19, "parent": 0}, {"command": 1, "file": 0, "line": 25, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 71, "parent": 3}, {"command": 3, "file": 0, "line": 9, "parent": 0}, {"command": 3, "file": 0, "line": 10, "parent": 0}, {"command": 3, "file": 0, "line": 11, "parent": 0}, {"command": 3, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 13, "parent": 0}, {"command": 3, "file": 0, "line": 14, "parent": 0}, {"command": 3, "file": 0, "line": 15, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}, {"backtrace": 5, "path": "/usr/include/fastdfs"}, {"backtrace": 5, "path": "/usr/include/fastcommon"}, {"backtrace": 5, "path": "/usr/local/include/hiredis"}, {"backtrace": 5, "path": "/usr/include/mysql"}, {"backtrace": 6, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/base"}, {"backtrace": 7, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/jsoncpp"}, {"backtrace": 8, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/mysql"}, {"backtrace": 9, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis"}, {"backtrace": 10, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api"}, {"backtrace": 11, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 25, 26, 27]}, {"compileCommandFragments": [{"fragment": "-g -g   -w"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}, {"backtrace": 5, "path": "/usr/include/fastdfs"}, {"backtrace": 5, "path": "/usr/include/fastcommon"}, {"backtrace": 5, "path": "/usr/local/include/hiredis"}, {"backtrace": 5, "path": "/usr/include/mysql"}, {"backtrace": 6, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/base"}, {"backtrace": 7, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/jsoncpp"}, {"backtrace": 8, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/mysql"}, {"backtrace": 9, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis"}, {"backtrace": 10, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api"}, {"backtrace": 11, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang"}], "language": "C", "sourceIndexes": [18, 20, 21, 22, 23, 24]}], "dependencies": [{"backtrace": 2, "id": "muduo_base::@af02cc837cde80c48ca0"}, {"backtrace": 2, "id": "muduo_net::@d48a3526fec38d23c536"}], "id": "tc_http_server::@1264b3fe9a35f9518ffd", "link": {"commandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\..\\muduo\\lib\\libmuduo_net.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lmysqlclient", "role": "libraries"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "..\\..\\..\\muduo\\lib\\libmuduo_base.a", "role": "libraries"}, {"fragment": "-lrt", "role": "libraries"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "tc_http_server", "nameOnDisk": "tc_http_server.exe", "paths": {"build": "application/tuchuang", "source": "application/tuchuang"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/main.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/http_parser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/http_parser_wrapper.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/http_conn.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/base/config_file_reader.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/base/tc_common.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/base/util.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_common.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_deal_sharefile.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_dealfile.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_login.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_md5.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_myfiles.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_register.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_sharefiles.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_sharepicture.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/api/api_upload.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/mysql/db_pool.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/async.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/redis/cache_pool.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/dict.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/hiredis.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/net.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/read.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/tuchuang/redis/sds.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/jsoncpp/json_reader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/jsoncpp/json_value.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "application/tuchuang/jsoncpp/json_writer.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}
# 这里的README先不用关注，后续性能测试的时候重新整理
 
 
 ./wrk -c 256 -t 4 -d 5s --latency -s  scripts/reg.lua  http://127.0.0.1/api/reg

 mysql写入性能优化

4. 配置数据库参数
优化数据库的配置参数可以有效提升插入性能。

修改my.cnf或者my.ini配置文件中的如下参数：
 [mysqld]
innodb_flush_log_at_trx_commit = 2  -- 降低日志写入的强度 有一定的作用
innodb_buffer_pool_size = 1G          -- 增大缓冲池大小
https://geek-docs.com/sql/sql-ask-answer/621_sql_how_to_improve_the_speed_of_innodb_writes_per_second_of_mysql_db.html
4. 使用多线程和并发
InnoDB存储引擎支持多线程写入，我们可以开启多个后台线程来处理并发的写入请求。通过增加innodb_write_io_threads和innodb_read_io_threads参数的值，可以提高并发写入和读取的能力。另外，我们还可以启用InnoDB的并发事务处理机制，通过配置innodb_thread_concurrency参数来控制并发线程的数量，从而提高写入性能。
-- 配置InnoDB的并发线程数量为16 作用不大
SET GLOBAL innodb_thread_concurrency = 16;

-- 配置InnoDB的并发写入线程数量为8
SET GLOBAL innodb_write_io_threads = 8;

-- 配置InnoDB的并发读取线程数量为8
SET GLOBAL innodb_read_io_threads = 8;



select 查询时不要用*


sudo ./bin/httpserver ./tc_http_server.conf 4 64
/wrk -c 256 -t 4 -d 5s --latency -s  scripts/reg.lua  http://127.0.0.1/api/reg
Running 5s test @ http://127.0.0.1/api/reg
Requests/sec:   2717.87

sudo systemctl  restart mysql.service


EXPLAIN  insert into user_info (`user_name`,`nick_name`,`password`,`phone`,`email`,`create_time`) values("Bob", "Bobby", "pass123", "13999998888", "<EMAIL>", "2024-12-11 13:22:22");

mysql> EXPLAIN  insert into user_info (`user_name`,`nick_name`,`password`,`phone`,`email`,`create_time`) values("Bob", "Bobby", "pass123", "13999998888", "<EMAIL>", "2024-12-11 13:22:22");
+----+-------------+-----------+------------+------+---------------+------+---------+------+------+----------+-------+
| id | select_type | table     | partitions | type | possible_keys | key  | key_len | ref  | rows | filtered | Extra |
+----+-------------+-----------+------------+------+---------------+------+---------+------+------+----------+-------+
|  1 | INSERT      | user_info | NULL       | ALL  | NULL          | NULL | NULL    | NULL | NULL |     NULL | NULL  |
+----+-------------+-----------+------------+------+---------------+------+---------+------+------+----------+-------+
1 row in set, 1 warning (0.00 sec)

EXPLAIN select id, user_name from user_info where user_name='lili';



我们执行如下命令实现数据插入，为提高效率这里开启 10 个客户端线程同时插入，总共执行 十万次操作：
create database hangge;
use hangge;
CREATE TABLE people (
    id INT AUTO_INCREMENT PRIMARY KEY,  -- 通常建议添加一个自增主键列用于唯一标识每条记录
    name VARCHAR(255),  -- 根据 uuid() 函数返回值的大致情况，这里设置合适的长度，可按需调整
    age INT  -- 用于存储年龄值
);


mysqlslap -uroot -p123456 --concurrency=10 --number-of-queries=100000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (uuid(), RAND() * 100)"

lqf@ubuntu:~/tuchuang-git/wrk_0voice$ mysqlslap -uroot -p123456 --concurrency=10 --number-of-queries=100000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (uuid(), RAND() * 100)"
mysqlslap: [Warning] Using a password on the command line interface can be insecure.
Benchmark
	Average number of seconds to run all queries: 19.909 seconds
	Minimum number of seconds to run all queries: 19.909 seconds
	Maximum number of seconds to run all queries: 19.909 seconds
	Number of clients running queries: 10
	Average number of queries per client: 10000
平均下来虚拟机里只有5000的插入性能。


mysqlslap -uroot -p123456 --concurrency=10 --number-of-queries=20000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (`dd`, RAND() * 100)"
mysqlslap -uroot -p123456 --concurrency=10 --number-of-queries=20000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (uuid(), RAND() * 100)"

TRUNCATE TABLE your_table_name;

lqf@ubuntu:/home/<USER>/storage/data/00/00$ mysqlslap -uroot -p123456 --concurrency=128 --number-of-queries=100000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (uuid(), RAND() * 100)"
mysqlslap: [Warning] Using a password on the command line interface can be insecure.
Benchmark
	Average number of seconds to run all queries: 9.667 seconds
	Minimum number of seconds to run all queries: 9.667 seconds
	Maximum number of seconds to run all queries: 9.667 seconds
	Number of clients running queries: 128
	Average number of queries per client: 781
并发数量增加后， qps可以到1w左右

mysqlslap -uroot -p123456 --concurrency=128 --number-of-queries=50000 --create-schema=hangge --query="INSERT INTO people(name,age) VALUES (uuid(), RAND() * 100)"
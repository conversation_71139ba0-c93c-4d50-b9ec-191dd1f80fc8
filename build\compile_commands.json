[{"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\AsyncLogging.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\AsyncLogging.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/AsyncLogging.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Condition.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Condition.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Condition.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\CountDownLatch.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\CountDownLatch.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/CountDownLatch.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\CurrentThread.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\CurrentThread.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/CurrentThread.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Date.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Date.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Date.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Exception.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Exception.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Exception.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\FileUtil.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\FileUtil.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/FileUtil.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\LogFile.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\LogFile.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/LogFile.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Logging.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Logging.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Logging.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\LogStream.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\LogStream.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/LogStream.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\ProcessInfo.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\ProcessInfo.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ProcessInfo.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Timestamp.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Timestamp.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Timestamp.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\Thread.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\Thread.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/Thread.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\ThreadPool.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\ThreadPool.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/ThreadPool.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\TimeZone.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\TimeZone.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/TimeZone.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/base", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_base.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_base.dir\\md5.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\base\\md5.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/base/md5.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Acceptor.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Acceptor.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Acceptor.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Buffer.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Buffer.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Buffer.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Channel.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Channel.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Channel.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Connector.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Connector.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Connector.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\EventLoop.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\EventLoop.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoop.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\EventLoopThread.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\EventLoopThread.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoopThread.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\EventLoopThreadPool.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\EventLoopThreadPool.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoopThreadPool.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\InetAddress.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\InetAddress.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/InetAddress.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Poller.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Poller.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Poller.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\poller\\DefaultPoller.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\poller\\DefaultPoller.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/poller/DefaultPoller.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\poller\\EPollPoller.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\poller\\EPollPoller.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/poller/EPollPoller.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\poller\\PollPoller.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\poller\\PollPoller.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/poller/PollPoller.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Socket.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Socket.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Socket.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -DNO_ACCEPT4 -o CMakeFiles\\muduo_net.dir\\SocketsOps.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\SocketsOps.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/SocketsOps.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\TcpClient.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\TcpClient.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpClient.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\TcpConnection.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\TcpConnection.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpConnection.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\TcpServer.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\TcpServer.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpServer.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\Timer.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\Timer.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Timer.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_net.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_net.dir\\TimerQueue.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\TimerQueue.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TimerQueue.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/http", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_http.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_http.dir\\HttpServer.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\http\\HttpServer.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/http/HttpServer.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/http", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_http.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_http.dir\\HttpContext.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\http\\HttpContext.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/http/HttpContext.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/inspect", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_inspect.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_inspect.dir\\Inspector.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\inspect\\Inspector.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/inspect/Inspector.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/inspect", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_inspect.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_inspect.dir\\PerformanceInspector.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\inspect\\PerformanceInspector.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/inspect/PerformanceInspector.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/inspect", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_inspect.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_inspect.dir\\ProcessInspector.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\inspect\\ProcessInspector.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/inspect/ProcessInspector.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/inspect", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/muduo_inspect.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\muduo_inspect.dir\\SystemInspector.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\muduo\\net\\inspect\\SystemInspector.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/inspect/SystemInspector.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\main.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\main.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/main.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\http_parser.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\http_parser.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/http_parser.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\http_parser_wrapper.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\http_parser_wrapper.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/http_parser_wrapper.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\http_conn.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\http_conn.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/http_conn.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\base\\config_file_reader.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\base\\config_file_reader.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/base/config_file_reader.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\base\\tc_common.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\base\\tc_common.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/base/tc_common.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\base\\util.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\base\\util.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/base/util.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_common.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_common.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_common.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_deal_sharefile.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_deal_sharefile.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_deal_sharefile.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_dealfile.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_dealfile.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_dealfile.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_login.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_login.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_login.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_md5.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_md5.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_md5.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_myfiles.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_myfiles.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_myfiles.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_register.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_register.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_register.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_sharefiles.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_sharefiles.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_sharefiles.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_sharepicture.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_sharepicture.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_sharepicture.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\api\\api_upload.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\api\\api_upload.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/api/api_upload.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\mysql\\db_pool.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\mysql\\db_pool.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/mysql/db_pool.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\async.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\async.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/async.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\cache_pool.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\cache_pool.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/cache_pool.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\dict.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\dict.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/dict.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\hiredis.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\hiredis.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/hiredis.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\net.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\net.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/net.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\read.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\read.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/read.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\gcc.exe  @CMakeFiles/tc_http_server.dir/includes_C.rsp -g -g   -w -o CMakeFiles\\tc_http_server.dir\\redis\\sds.c.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\redis\\sds.c", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/redis/sds.c"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\jsoncpp\\json_reader.cpp.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\jsoncpp\\json_reader.cpp", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/jsoncpp/json_reader.cpp"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\jsoncpp\\json_value.cpp.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\jsoncpp\\json_value.cpp", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/jsoncpp/json_value.cpp"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/application/tuchuang", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc_http_server.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc_http_server.dir\\jsoncpp\\json_writer.cpp.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\application\\tuchuang\\jsoncpp\\json_writer.cpp", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/application/tuchuang/jsoncpp/json_writer.cpp"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/client/1-upload", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc-client.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc-client.dir\\tc-client.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\client\\1-upload\\tc-client.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/client/1-upload/tc-client.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/client/1-upload", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc-upload.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc-upload.dir\\tc-upload.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\client\\1-upload\\tc-upload.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/client/1-upload/tc-upload.cc"}, {"directory": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/client/2-upload", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe  @CMakeFiles/tc-client2.dir/includes_CXX.rsp -DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w -o CMakeFiles\\tc-client2.dir\\tc-client.cc.obj -c C:\\Users\\<USER>\\Downloads\\tuchuang-master\\tuchuang-master\\tc-src\\client\\2-upload\\tc-client.cc", "file": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/client/2-upload/tc-client.cc"}]
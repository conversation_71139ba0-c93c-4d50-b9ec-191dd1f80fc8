# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

# Include any dependencies generated for this target.
include client/1-upload/CMakeFiles/tc-upload.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include client/1-upload/CMakeFiles/tc-upload.dir/compiler_depend.make

# Include the progress variables for this target.
include client/1-upload/CMakeFiles/tc-upload.dir/progress.make

# Include the compile flags for this target's objects.
include client/1-upload/CMakeFiles/tc-upload.dir/flags.make

client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj: client/1-upload/CMakeFiles/tc-upload.dir/flags.make
client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj: client/1-upload/CMakeFiles/tc-upload.dir/includes_CXX.rsp
client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj: ../client/1-upload/tc-upload.cc
client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj: client/1-upload/CMakeFiles/tc-upload.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj -MF CMakeFiles\tc-upload.dir\tc-upload.cc.obj.d -o CMakeFiles\tc-upload.dir\tc-upload.cc.obj -c C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\1-upload\tc-upload.cc

client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tc-upload.dir/tc-upload.cc.i"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\1-upload\tc-upload.cc > CMakeFiles\tc-upload.dir\tc-upload.cc.i

client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tc-upload.dir/tc-upload.cc.s"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload && C:\msys64\ucrt64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\1-upload\tc-upload.cc -o CMakeFiles\tc-upload.dir\tc-upload.cc.s

# Object files for target tc-upload
tc__upload_OBJECTS = \
"CMakeFiles/tc-upload.dir/tc-upload.cc.obj"

# External object files for target tc-upload
tc__upload_EXTERNAL_OBJECTS =

bin/tc-upload.exe: client/1-upload/CMakeFiles/tc-upload.dir/tc-upload.cc.obj
bin/tc-upload.exe: client/1-upload/CMakeFiles/tc-upload.dir/build.make
bin/tc-upload.exe: client/1-upload/CMakeFiles/tc-upload.dir/linklibs.rsp
bin/tc-upload.exe: client/1-upload/CMakeFiles/tc-upload.dir/objects1.rsp
bin/tc-upload.exe: client/1-upload/CMakeFiles/tc-upload.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ..\..\bin\tc-upload.exe"
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\tc-upload.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
client/1-upload/CMakeFiles/tc-upload.dir/build: bin/tc-upload.exe
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/build

client/1-upload/CMakeFiles/tc-upload.dir/clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload && $(CMAKE_COMMAND) -P CMakeFiles\tc-upload.dir\cmake_clean.cmake
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/clean

client/1-upload/CMakeFiles/tc-upload.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\client\1-upload C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\client\1-upload\CMakeFiles\tc-upload.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : client/1-upload/CMakeFiles/tc-upload.dir/depend


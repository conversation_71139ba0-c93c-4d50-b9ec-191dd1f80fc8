#ifndef __EFFICIENT_ROUTER_H__
#define __EFFICIENT_ROUTER_H__

#include <unordered_map>
#include <functional>
#include <string>
#include <string_view>

/**
 * 高效路由器实现方案
 * 提供多种路由匹配策略，比传统的 strncmp 链式比较更高效
 */

// 方案1: 基于哈希表的精确匹配路由
class HashRouter {
public:
    using Handler = std::function<int(const std::string&, const std::string&)>;
    
    // 注册路由
    void RegisterRoute(const std::string& path, Handler handler) {
        routes_[path] = std::move(handler);
    }
    
    // 路由匹配和执行 - O(1) 时间复杂度
    bool Route(const std::string& path, const std::string& content) {
        auto it = routes_.find(path);
        if (it != routes_.end()) {
            return it->second(path, content) == 0;
        }
        return false;
    }
    
private:
    std::unordered_map<std::string, Handler> routes_;
};

// 方案2: 基于前缀树(Trie)的路由 - 支持前缀匹配
class TrieRouter {
public:
    using Handler = std::function<int(const std::string&, const std::string&)>;
    
    struct TrieNode {
        std::unordered_map<char, std::unique_ptr<TrieNode>> children;
        Handler handler;
        bool is_endpoint = false;
    };
    
    TrieRouter() : root_(std::make_unique<TrieNode>()) {}
    
    // 注册路由
    void RegisterRoute(const std::string& path, Handler handler) {
        TrieNode* current = root_.get();
        for (char c : path) {
            if (current->children.find(c) == current->children.end()) {
                current->children[c] = std::make_unique<TrieNode>();
            }
            current = current->children[c].get();
        }
        current->handler = std::move(handler);
        current->is_endpoint = true;
    }
    
    // 路由匹配 - 支持前缀匹配
    bool Route(const std::string& path, const std::string& content) {
        TrieNode* current = root_.get();
        TrieNode* last_valid = nullptr;
        
        for (char c : path) {
            if (current->children.find(c) == current->children.end()) {
                break;
            }
            current = current->children[c].get();
            if (current->is_endpoint) {
                last_valid = current;
            }
        }
        
        if (last_valid && last_valid->handler) {
            return last_valid->handler(path, content) == 0;
        }
        return false;
    }
    
private:
    std::unique_ptr<TrieNode> root_;
};

// 方案3: 基于字符串视图的快速比较路由
class FastStringRouter {
public:
    using Handler = std::function<int(const std::string&, const std::string&)>;
    
    struct Route {
        std::string_view prefix;
        Handler handler;
        
        Route(std::string_view p, Handler h) : prefix(p), handler(std::move(h)) {}
    };
    
    // 注册路由 - 按长度排序，长的优先匹配
    void RegisterRoute(const std::string& path, Handler handler) {
        routes_.emplace_back(path, std::move(handler));
        // 按前缀长度降序排序，确保长前缀优先匹配
        std::sort(routes_.begin(), routes_.end(), 
                 [](const Route& a, const Route& b) {
                     return a.prefix.length() > b.prefix.length();
                 });
    }
    
    // 快速前缀匹配 - 使用 string_view 避免字符串拷贝
    bool Route(const std::string& path, const std::string& content) {
        std::string_view path_view(path);
        
        for (const auto& route : routes_) {
            if (path_view.starts_with(route.prefix)) {
                return route.handler(path, content) == 0;
            }
        }
        return false;
    }
    
private:
    std::vector<Route> routes_;
};

// 方案4: 基于完美哈希的编译时路由 (C++20)
#if __cplusplus >= 202002L
#include <array>

template<size_t N>
class PerfectHashRouter {
public:
    using Handler = std::function<int(const std::string&, const std::string&)>;
    
    struct RouteEntry {
        std::string_view path;
        Handler handler;
    };
    
    constexpr PerfectHashRouter() = default;
    
    // 编译时计算哈希值
    static constexpr size_t hash(std::string_view str) {
        size_t hash = 5381;
        for (char c : str) {
            hash = ((hash << 5) + hash) + c;
        }
        return hash % N;
    }
    
    void RegisterRoute(std::string_view path, Handler handler) {
        size_t index = hash(path);
        routes_[index] = {path, std::move(handler)};
    }
    
    bool Route(const std::string& path, const std::string& content) {
        size_t index = hash(path);
        const auto& entry = routes_[index];
        
        if (entry.path == path && entry.handler) {
            return entry.handler(path, content) == 0;
        }
        return false;
    }
    
private:
    std::array<RouteEntry, N> routes_{};
};
#endif

#endif // __EFFICIENT_ROUTER_H__

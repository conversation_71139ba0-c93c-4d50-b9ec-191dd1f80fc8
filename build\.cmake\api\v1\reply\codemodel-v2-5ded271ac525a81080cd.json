{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 5, 7], "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.0"}, "projectIndex": 0, "source": "."}, {"build": "muduo/base", "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 0, "projectIndex": 0, "source": "muduo/base", "targetIndexes": [0]}, {"build": "muduo/net", "childIndexes": [3, 4], "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 0, "projectIndex": 0, "source": "muduo/net", "targetIndexes": [3]}, {"build": "muduo/net/http", "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 2, "projectIndex": 0, "source": "muduo/net/http", "targetIndexes": [1]}, {"build": "muduo/net/inspect", "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 2, "projectIndex": 0, "source": "muduo/net/inspect", "targetIndexes": [2]}, {"build": "application", "childIndexes": [6], "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 0, "projectIndex": 0, "source": "application"}, {"build": "application/tuchuang", "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 5, "projectIndex": 0, "source": "application/tuchuang", "targetIndexes": [7]}, {"build": "client", "childIndexes": [8, 9], "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 0, "projectIndex": 0, "source": "client"}, {"build": "client/1-upload", "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 7, "projectIndex": 0, "source": "client/1-upload", "targetIndexes": [4, 6]}, {"build": "client/2-upload", "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 7, "projectIndex": 0, "source": "client/2-upload", "targetIndexes": [5]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "name": "tuch<PERSON>", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 1, "id": "muduo_base::@af02cc837cde80c48ca0", "jsonFile": "target-muduo_base-Debug-26e82309c87f8e019e05.json", "name": "muduo_base", "projectIndex": 0}, {"directoryIndex": 3, "id": "muduo_http::@5787850c391ef1a35796", "jsonFile": "target-muduo_http-Debug-6d542a007dbb31763f9f.json", "name": "muduo_http", "projectIndex": 0}, {"directoryIndex": 4, "id": "muduo_inspect::@403560d5477bdc04a513", "jsonFile": "target-muduo_inspect-Debug-b1a34e640afce5cfcfdf.json", "name": "muduo_inspect", "projectIndex": 0}, {"directoryIndex": 2, "id": "muduo_net::@d48a3526fec38d23c536", "jsonFile": "target-muduo_net-Debug-d7d89accac14a8ee87f2.json", "name": "muduo_net", "projectIndex": 0}, {"directoryIndex": 8, "id": "tc-client::@214c07589b956b7e49bc", "jsonFile": "target-tc-client-Debug-e3c9954003e53d602cc4.json", "name": "tc-client", "projectIndex": 0}, {"directoryIndex": 9, "id": "tc-client2::@7f5747c75b45c724e16e", "jsonFile": "target-tc-client2-Debug-66e34c32f3ed03810583.json", "name": "tc-client2", "projectIndex": 0}, {"directoryIndex": 8, "id": "tc-upload::@214c07589b956b7e49bc", "jsonFile": "target-tc-upload-Debug-c47697914f6099f0b2b2.json", "name": "tc-upload", "projectIndex": 0}, {"directoryIndex": 6, "id": "tc_http_server::@1264b3fe9a35f9518ffd", "jsonFile": "target-tc_http_server-Debug-395d904dfaa265532019.json", "name": "tc_http_server", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build", "source": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}, "version": {"major": 2, "minor": 2}}
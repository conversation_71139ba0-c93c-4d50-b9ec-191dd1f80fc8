#include "api_upload.h"

#include <unistd.h>
#include <sys/wait.h>


// 如果文件已经存在则删除对应的文件，这样减少同样文件占用空间
int storeFileinfo(CDBConn *db_conn, CacheConn *cache_conn, FileInfo &file_info, bool &file_exsit) {
    int ret = 0;
    time_t now;
    char create_time[TIME_STRING_LEN];
    char sql_cmd[SQL_MAX_LEN] = {0};
    // sql 语句
    /*
       -- =============================================== 文件信息表
       -- md5 文件md5
       -- file_id 文件id
       -- url 文件url
       -- size 文件大小, 以字节为单位
       -- type 文件类型： png, zip, mp4……
       -- count 文件引用计数， 默认为1， 每增加一个用户拥有此文件，此计数器+1
       */
    ScopedFileInfoLock lock(FileInfoLock::GetInstance(), 1000);
    if (lock.IsLocked())   {
        sprintf(sql_cmd,
            "insert into file_info (md5, file_id, url, size, type, count) "
            "values ('%s', '%s', '%s', '%ld', '%s', %d)",
            file_info.file_md5.c_str(), file_info.file_id.c_str(), 
            file_info.file_url.c_str(), file_info.file_size, file_info.file_type.c_str(), 1);
        LOG_INFO << "执行: " <<  sql_cmd;
        if (!db_conn->ExecuteCreate(sql_cmd)) //执行sql语句
        {
            LOG_ERROR << sql_cmd << " 操作失败， try  update file_info";
            // 此时需要查询 file_info 是否已经存在对应md5的记录了，如果已经存在 可以直接增加引用计数
            sprintf(sql_cmd, "update file_info set count = count +1 where md5 = '%s'",  
            file_info.file_md5.c_str());
            LOG_INFO << "执行: " << sql_cmd;
            if (!db_conn->ExecutePassQuery(sql_cmd)) {
                LOG_ERROR << sql_cmd << " 操作失败, 可能数据库已经异常了";
                ret = -1;
                goto END;
            }
            file_exsit = true;      //同样的文件已经存在了
        }
    } else {
        LOG_ERROR << "FileInfoLock TryLockFor" << "超时";
        ret = -1;
        goto END;
    }
    
    //获取当前时间
    now = time(NULL);
    strftime(create_time, TIME_STRING_LEN - 1, "%Y-%m-%d %H:%M:%S", localtime(&now));

    /*
       -- =============================================== 用户文件列表
       -- user 文件所属用户
       -- md5 文件md5
       -- create_time 文件创建时间
       -- file_name 文件名字
       -- shared_status 共享状态, 0为没有共享， 1为共享
       -- pv 文件下载量，默认值为0，下载一次加1
       */
    // sql语句
    sprintf(sql_cmd,
            "insert into user_file_list(user, md5, create_time, file_name, "
            "shared_status, pv) values ('%s', '%s', '%s', '%s', %d, %d)",
            file_info.file_user.c_str(),  file_info.file_md5.c_str(), 
            create_time, file_info.file_name.c_str(), 0, 0);
    LOG_INFO << "执行: " <<  sql_cmd;
    if (!db_conn->ExecuteCreate(sql_cmd)) {
        LOG_ERROR << sql_cmd << " 操作失败";
        ret = -1;
        goto END;
    }

    // 询用户文件数量+1      web热点 大明星  微博存在缓存里面。
    // if (CacheIncrCount(cache_conn, string(user)) < 0) {
    //     LOG_ERROR << " CacheIncrCount 操作失败";
    // }

END:
    return ret;
}

int ApiUploadFile(FileInfo &file_info, string &resp_json){
     // 获取数据库连接
    CDBManager *db_manager = CDBManager::getInstance();
    CDBConn *db_conn = db_manager->GetDBConn("tuchuang_master"); // 连接池可以配置多个 分库
    AUTO_REL_DBCONN(db_manager, db_conn);
    bool file_exsit = false;
    Json::Value root;    
    // 把文件写入file_info
    int ret = storeFileinfo(db_conn, NULL, file_info , file_exsit);
    if(ret < 0 || file_exsit) {
        // 严谨而言，这里需要删除 已经上传的文件
         //从storage服务器删除此文件，参数为为文件id
        if (RemoveFileFromFastDfs(file_info.file_id.c_str()) != 0) {
            LOG_ERROR << "RemoveFileFromFastDfs failed";
        }
    }
    if (ret < 0) {
        LOG_ERROR << "storeFileinfo failed ";
        ret = -1;
        goto END;
    }
   
    ret = 0;
    root["code"] = 0;
    resp_json = root.toStyledString(); // json序列化,  直接用writer是紧凑方式，这里toStyledString是格式化更可读方式

    return 0;
END:
    root["code"] = 1;
    resp_json = root.toStyledString(); // json序列化

    return -1;
}

 
#ifndef FILE_UPLOAD_CONTEXT_HPP
#define FILE_UPLOAD_CONTEXT_HPP

#include <string>
#include <cstdint>
#include "muduo/base/Atomic.h"

class FileUploadContext {
public:

    enum class State {  //目前这里没有使用
        kExpectHeaders,    // 等待头部
        kExpectContent,    // 等待文件内容
        kExpectBoundary,   // 等待边界
        kComplete         // 上传完成
    };
    //根据目前的结构处理
    enum class FileTypeState { //目前使用这里的状态机
        kFileName,
        kFileBody,
        kFileUser,
        kFileMd5,
        kFileSize,
        kFileComplete
    };

    FileUploadContext(const std::string& originalFilename);
    ~FileUploadContext();

    // 连接FastDFS服务器
    bool connect(const std::string& config_file = "/etc/fdfs/client.conf");
    bool uploadBlock(const char* data, size_t size);

    // 首次上传文件
    bool uploadFirstBlock(const char* data, size_t size);

    // 追加文件内容
    bool appendBlock(const char* data, size_t size);

    // 关闭连接
    void disconnect();
    void setBoundary(const std::string& boundary) { boundary_ = boundary; }
    State getState() const { return state_; }
    void setState(State state) { state_ = state; }
    FileTypeState getFileTypeState() const { return file_type_state_; }
    void setExpectFileTypeState(FileTypeState file_type_state) { file_type_state_ = file_type_state;}
    const std::string& getBoundary() const { return boundary_; }
    uintmax_t getTotalBytes() const { return totalBytes_; }
    const std::string& getOriginalFilename() const { return originalFilename_; }
    const std::string& getFileId() const { return file_id_; }
    void setUser(std::string &user) { user_ = user;}
    const std::string& getUser() const { return user_;}
    void setMd5(std::string &md5) { md5_ = md5;}
    const std::string& getMd5() const { return md5_;}
    void setSize(uint64_t size) { size_ = size;}
    uint64_t getSize() const { return size_;}

    const std::string& getFileType() const { return file_type_;}
    const std::string& getFileUrl() const { return file_url_;}
private:
    void* tracker_server_ = nullptr; //ConnectionInfo
    void* storage_server_ = nullptr;
    char group_name_[16];  // FDFS_GROUP_NAME_MAX_LEN + 1
    int store_path_index_;
    bool connected_;
     std::string originalFilename_; // 原始文件名
    uintmax_t totalBytes_;          //总字节
    State state_;                 // 当前状态
    std::string boundary_;        // multipart边界
    std::string file_ext_name_;   // 文件扩展名
    std::string file_id_;         // 文件ID
    bool is_first_block_ = true;         // 是否是第一个块
    muduo::AtomicUint64 file_id_generator_;
    FileTypeState file_type_state_ = FileTypeState::kFileName; //获取文件名阶段
    std::string user_;
    uint64_t size_ = 0;
    std::string md5_;
    std::string file_type_;
    std::string file_url_;
};

#endif // FDFS_CLIENT_HPP 
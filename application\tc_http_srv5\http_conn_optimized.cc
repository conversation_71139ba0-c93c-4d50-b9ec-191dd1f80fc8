#include "http_conn.h"
#include "efficient_router.h"
#include "muduo/base/Logging.h"

// 静态路由表定义
std::unordered_map<std::string, CHttpConn::RouteHandler> CHttpConn::route_table_;

// 方案1: 使用哈希表路由的优化实现
void CHttpConn::OnRead(Buffer *buf) {
    const char *in_buf = buf->peek();
    size_t buf_len = buf->readableBytes();
    http_parser_.ParseHttpContent(in_buf, buf_len);
    
    if (http_parser_.IsReadAll()) {
        string url = http_parser_.GetUrlString();
        string content = http_parser_.GetBodyContentString();
        LOG_INFO << "url: " << url << ", content: " << content;
        
        // 初始化路由表（只在第一次调用时执行）
        if (route_table_.empty()) {
            InitRouteTable();
        }
        
        // O(1) 精确匹配 - 简化版本
        auto it = route_table_.find(url);
        if (it != route_table_.end()) {
            // 找到匹配路由，直接执行
            it->second(this, url, content);
        } else {
            // 未找到路由，返回404
            HandleUnknownRoute(url, content);
        }
    }
}

void CHttpConn::InitRouteTable() {
    // 注册所有路由 - 使用 lambda 包装成员函数
    route_table_["/api/reg"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleRegisterRequest(url, content);
    };
    
    route_table_["/api/login"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleLoginRequest(url, content);
    };
    
    route_table_["/api/md5"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleMd5Request(url, content);
    };
    
    route_table_["/api/upload"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleUploadRequest(url, content);
    };
    
    route_table_["/api/myfiles"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleMyFilesRequest(url, content);
    };
    
    route_table_["/api/sharepic"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleSharepictureRequest(url, content);
    };
    
    route_table_["/api/dealfile"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleDealfileRequest(url, content);
    };
    
    route_table_["/api/sharefiles"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleSharefilesRequest(url, content);
    };
    
    route_table_["/api/dealsharefile"] = [](CHttpConn* conn, string& url, string& content) {
        return conn->_HandleDealsharefileRequest(url, content);
    };
}

void CHttpConn::HandleUnknownRoute(const string& url, const string& content) {
    LOG_ERROR << "url unknown, url= " << url;
    char *resp_content = new char[256];
    string str_json = "{\"code\": 1, \"msg\": \"unknown route\"}"; 
    uint32_t len_json = str_json.size();
    
    snprintf(resp_content, 256, 
             "HTTP/1.1 404 Not Found\r\n"
             "Content-Length: %d\r\n"
             "Content-Type: application/json\r\n\r\n%s",
             len_json, str_json.c_str());
    
    tcp_conn_->send(resp_content);
    delete[] resp_content;
}

// 方案2: 使用 Trie 树路由的实现示例
class TrieBasedHttpConn {
private:
    TrieRouter router_;
    
public:
    void InitTrieRouter() {
        router_.RegisterRoute("/api/reg", [this](const string& url, const string& content) {
            return this->HandleRegister(url, content);
        });
        
        router_.RegisterRoute("/api/login", [this](const string& url, const string& content) {
            return this->HandleLogin(url, content);
        });
        
        // ... 其他路由注册
    }
    
    void OnRead(Buffer *buf) {
        // HTTP 解析逻辑...
        string url = ""; // 从解析器获取
        string content = ""; // 从解析器获取
        
        // 使用 Trie 路由
        if (!router_.Route(url, content)) {
            HandleUnknownRoute(url, content);
        }
    }
    
    int HandleRegister(const string& url, const string& content) { return 0; }
    int HandleLogin(const string& url, const string& content) { return 0; }
    void HandleUnknownRoute(const string& url, const string& content) {}
};

// 方案3: 使用快速字符串路由的实现示例
class FastStringBasedHttpConn {
private:
    FastStringRouter router_;
    
public:
    void InitFastStringRouter() {
        // 注册路由时会自动按长度排序
        router_.RegisterRoute("/api/dealsharefile", [this](const string& url, const string& content) {
            return this->HandleDealShareFile(url, content);
        });
        
        router_.RegisterRoute("/api/sharefiles", [this](const string& url, const string& content) {
            return this->HandleShareFiles(url, content);
        });
        
        router_.RegisterRoute("/api/sharepic", [this](const string& url, const string& content) {
            return this->HandleSharePic(url, content);
        });
        
        router_.RegisterRoute("/api/myfiles", [this](const string& url, const string& content) {
            return this->HandleMyFiles(url, content);
        });
        
        router_.RegisterRoute("/api/dealfile", [this](const string& url, const string& content) {
            return this->HandleDealFile(url, content);
        });
        
        router_.RegisterRoute("/api/upload", [this](const string& url, const string& content) {
            return this->HandleUpload(url, content);
        });
        
        router_.RegisterRoute("/api/login", [this](const string& url, const string& content) {
            return this->HandleLogin(url, content);
        });
        
        router_.RegisterRoute("/api/reg", [this](const string& url, const string& content) {
            return this->HandleRegister(url, content);
        });
        
        router_.RegisterRoute("/api/md5", [this](const string& url, const string& content) {
            return this->HandleMd5(url, content);
        });
    }
    
    void OnRead(Buffer *buf) {
        // HTTP 解析逻辑...
        string url = ""; // 从解析器获取
        string content = ""; // 从解析器获取
        
        // 使用快速字符串路由 - 支持前缀匹配
        if (!router_.Route(url, content)) {
            HandleUnknownRoute(url, content);
        }
    }
    
    // 处理函数声明...
    int HandleRegister(const string& url, const string& content) { return 0; }
    int HandleLogin(const string& url, const string& content) { return 0; }
    int HandleMd5(const string& url, const string& content) { return 0; }
    int HandleUpload(const string& url, const string& content) { return 0; }
    int HandleMyFiles(const string& url, const string& content) { return 0; }
    int HandleSharePic(const string& url, const string& content) { return 0; }
    int HandleDealFile(const string& url, const string& content) { return 0; }
    int HandleShareFiles(const string& url, const string& content) { return 0; }
    int HandleDealShareFile(const string& url, const string& content) { return 0; }
    void HandleUnknownRoute(const string& url, const string& content) {}
};

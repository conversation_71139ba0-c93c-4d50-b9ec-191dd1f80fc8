# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\net\inspect\\CMakeFiles\progress.marks
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule
.PHONY : muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule

# Convenience name for target.
muduo_inspect: muduo/net/inspect/CMakeFiles/muduo_inspect.dir/rule
.PHONY : muduo_inspect

# fast build rule for target.
muduo_inspect/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/build
.PHONY : muduo_inspect/fast

Inspector.obj: Inspector.cc.obj
.PHONY : Inspector.obj

# target to build an object file
Inspector.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/Inspector.cc.obj
.PHONY : Inspector.cc.obj

Inspector.i: Inspector.cc.i
.PHONY : Inspector.i

# target to preprocess a source file
Inspector.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/Inspector.cc.i
.PHONY : Inspector.cc.i

Inspector.s: Inspector.cc.s
.PHONY : Inspector.s

# target to generate assembly for a file
Inspector.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/Inspector.cc.s
.PHONY : Inspector.cc.s

PerformanceInspector.obj: PerformanceInspector.cc.obj
.PHONY : PerformanceInspector.obj

# target to build an object file
PerformanceInspector.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/PerformanceInspector.cc.obj
.PHONY : PerformanceInspector.cc.obj

PerformanceInspector.i: PerformanceInspector.cc.i
.PHONY : PerformanceInspector.i

# target to preprocess a source file
PerformanceInspector.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/PerformanceInspector.cc.i
.PHONY : PerformanceInspector.cc.i

PerformanceInspector.s: PerformanceInspector.cc.s
.PHONY : PerformanceInspector.s

# target to generate assembly for a file
PerformanceInspector.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/PerformanceInspector.cc.s
.PHONY : PerformanceInspector.cc.s

ProcessInspector.obj: ProcessInspector.cc.obj
.PHONY : ProcessInspector.obj

# target to build an object file
ProcessInspector.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/ProcessInspector.cc.obj
.PHONY : ProcessInspector.cc.obj

ProcessInspector.i: ProcessInspector.cc.i
.PHONY : ProcessInspector.i

# target to preprocess a source file
ProcessInspector.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/ProcessInspector.cc.i
.PHONY : ProcessInspector.cc.i

ProcessInspector.s: ProcessInspector.cc.s
.PHONY : ProcessInspector.s

# target to generate assembly for a file
ProcessInspector.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/ProcessInspector.cc.s
.PHONY : ProcessInspector.cc.s

SystemInspector.obj: SystemInspector.cc.obj
.PHONY : SystemInspector.obj

# target to build an object file
SystemInspector.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/SystemInspector.cc.obj
.PHONY : SystemInspector.cc.obj

SystemInspector.i: SystemInspector.cc.i
.PHONY : SystemInspector.i

# target to preprocess a source file
SystemInspector.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/SystemInspector.cc.i
.PHONY : SystemInspector.cc.i

SystemInspector.s: SystemInspector.cc.s
.PHONY : SystemInspector.s

# target to generate assembly for a file
SystemInspector.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\net\inspect\CMakeFiles\muduo_inspect.dir\build.make muduo/net/inspect/CMakeFiles/muduo_inspect.dir/SystemInspector.cc.s
.PHONY : SystemInspector.cc.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... muduo_inspect
	@echo ... Inspector.obj
	@echo ... Inspector.i
	@echo ... Inspector.s
	@echo ... PerformanceInspector.obj
	@echo ... PerformanceInspector.i
	@echo ... PerformanceInspector.s
	@echo ... ProcessInspector.obj
	@echo ... ProcessInspector.i
	@echo ... ProcessInspector.s
	@echo ... SystemInspector.obj
	@echo ... SystemInspector.i
	@echo ... SystemInspector.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


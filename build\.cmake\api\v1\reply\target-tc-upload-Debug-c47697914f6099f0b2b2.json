{"artifacts": [{"path": "bin/tc-upload.exe"}, {"path": "bin/tc-upload.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "TARGET_LINK_LIBRARIES", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["client/1-upload/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 71, "parent": 3}, {"command": 3, "file": 0, "line": 2, "parent": 0}, {"command": 3, "file": 0, "line": 4, "parent": 0}, {"command": 3, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g   -w"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src"}, {"backtrace": 5, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src.."}, {"backtrace": 6, "path": "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/client"}, {"backtrace": 7, "path": "/usr/include/jsoncpp"}], "language": "CXX", "sourceIndexes": [0]}], "id": "tc-upload::@214c07589b956b7e49bc", "link": {"commandFragments": [{"fragment": "-DCHECK_PTHREAD_RETURN_VALUE -D_FILE_OFFSET_BITS=64 -Wall -Wextra -Werror -Wconversion -Wno-unused-parameter -Wold-style-cast -Woverloaded-virtual -Wpointer-arith -Wshadow -Wwrite-strings -march=native -std=c++17 -rdynamic -O0 -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "tc-upload", "nameOnDisk": "tc-upload.exe", "paths": {"build": "client/1-upload", "source": "client/1-upload"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "client/1-upload/tc-upload.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}
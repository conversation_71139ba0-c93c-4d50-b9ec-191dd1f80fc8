# Install script for directory: C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/tuchuang")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "C:/msys64/ucrt64/bin/objdump.exe")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/lib/libmuduo_net.a")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/muduo/net" TYPE FILE FILES
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Buffer.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Callbacks.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Channel.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/Endian.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoop.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoopThread.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/EventLoopThreadPool.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/InetAddress.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpClient.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpConnection.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TcpServer.h"
    "C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/muduo/net/TimerId.h"
    )
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/http/cmake_install.cmake")
  include("C:/Users/<USER>/Downloads/tuchuang-master/tuchuang-master/tc-src/build/muduo/net/inspect/cmake_install.cmake")

endif()


#ifndef HTTP_HANDLER_HPP
#define HTTP_HANDLER_HPP

#include <regex>
#include <random>
#include <any>
#include "muduo/net/TcpServer.h"
#include  "muduo/net/TcpConnection.h"
#include "muduo/base/ThreadPool.h"

#include "muduo/net/EventLoop.h"  //EventLoop
#include "muduo/base/Logging.h" // Logger日志头文件
#include "muduo/net/http/HttpRequest.h"
#include "muduo/net/http/HttpResponse.h"
#include "muduo/net/http/HttpContext.h"

#include "upload/file_upload_context.h"
#include "api/api_register.h"
#include "api/api_login.h"
#include "api/api_md5.h"
#include "api/api_myfiles.h"
#include "api/api_sharepicture.h"
#include "api/api_dealfile.h"
#include "api/api_sharefiles.h"
#include "api/api_deal_sharefile.h"
#include "api/api_upload.h"
 
using namespace muduo;
using namespace muduo::net;

class HttpHandler {
    #if 1
private:
     
    // 定义处理函数类型
    using RequestHandler = bool (HttpHandler::*)(const TcpConnectionPtr&, HttpRequest&, HttpResponse*);
    
    // 路由模式结构
    struct RoutePattern {
        std::regex pattern;              // 正则表达式模式
        std::vector<std::string> params; // 路径参数名列表
        RequestHandler handler;          // 处理函数
        HttpRequest::Method method;      // HTTP方法

        RoutePattern(const std::string& pattern_str, 
                    const std::vector<std::string>& param_names,
                    RequestHandler h,
                    HttpRequest::Method m)
            : pattern(pattern_str)
            , params(param_names)
            , handler(h)
            , method(m)
        {}
    };

    // 路由表
    std::vector<RoutePattern> routes_;
 

public:
    HttpHandler(int numThreads)
    {
        numThreads = numThreads;
    
        // 初始化路由表
        initRoutes();
    }

    ~HttpHandler() {
        
    }


    // 返回 true 表示同步处理完成，false 表示异步处理
    bool onRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
        std::string path = req.path();
        LOG_INFO << "Headers " << req.methodString() << " " << path;
        LOG_INFO << "Content-Type: " << req.getHeader("Content-Type");
        LOG_INFO << "Body size: " << req.body().size();

        try {
            // 查找匹配的路由
            for (const auto& route : routes_) {
                if (route.method != req.method()) {
                    LOG_INFO << "Method mismatch: expected " << route.method << ", got " << req.method();
                    continue;
                }

                std::smatch matches;
                if (std::regex_match(path, matches, route.pattern)) {
                    LOG_INFO << "Found matching route: " << path;
                    // 提取路径参数
                    std::unordered_map<std::string, std::string> params;
                    for (size_t i = 0; i < route.params.size() && i + 1 < matches.size(); ++i) {
                        params[route.params[i]] = matches[i + 1];
                    }

                    // 将参数存储到请求对象中
                    req.setPathParams(params);

                    // 调用处理函数
                    return (this->*route.handler)(conn, req, resp);
                }
            }

            // 未找到匹配的路由，返回404
            LOG_WARN << "No matching route found for " << path;
            return handleNotFound(conn, resp);
        }
        catch (const std::exception& e) {
            LOG_ERROR << "Error processing request: " << e.what();
            sendError(resp, "Internal Server Error", HttpResponse::k500InternalServerError, conn);
            return true;
        }
    }

private:
    // 设置 HTTP 响应的通用函数
    void setHttpResponse(HttpResponse* resp, 
            HttpResponse::HttpStatusCode code,
            const std::string& message,
            const std::string& resp_json) 
    {
        resp->setStatusCode(code);
        resp->setStatusMessage(message);
        resp->setContentType("application/json");
        resp->addHeader("Connection", "close");
        resp->setBody(resp_json);
    }
    /*
    ------WebKitFormBoundaryMlKbOVq1TUAbtOBd
Content-Disposition: form-data; name="file"; filename="upload.txt"
Content-Type: text/plain

 const std::any& getContext() const
  { return context_; }

  std::any* getMutableContext()
  { return &context_; }
------WebKitFormBoundaryMlKbOVq1TUAbtOBd
Content-Disposition: form-data; name="user"

darren
------WebKitFormBoundaryMlKbOVq1TUAbtOBd
Content-Disposition: form-data; name="md5"

44f0fcb4f0a93023557a3ce3bc20e9b5
------WebKitFormBoundaryMlKbOVq1TUAbtOBd
Content-Disposition: form-data; name="size"

119
------WebKitFormBoundaryMlKbOVq1TUAbtOBd-- 
*/
    bool handleFileUpload(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
        // 验证会话
        std::string sessionId = req.getHeader("X-Session-ID");
        int userId;
        std::string usernameFromSession;
        
        // 获取 HttpContext
        HttpContext *httpContext = std::any_cast<HttpContext>(conn->getMutableContext());
        if (!httpContext) {
            LOG_ERROR << "HttpContext is null";
            sendError(resp, "Internal Server Error", HttpResponse::k500InternalServerError, conn);
            return true;
        }
        LOG_INFO << "body.size() = " << req.body().size();
        // LOG_INFO << "body: " << req.body;
        // 尝试获取已存在的上传上下文
        std::shared_ptr<FileUploadContext> uploadContext = httpContext->getContext<FileUploadContext>();
        string body = req.body();
        LOG_INFO << "body: " << body;
        // 使用指针记录当前处理位置
        size_t currentPos = 0;
        size_t bodyEndPos = body.size();
        size_t nextBoundary = 0;
        std::string boundary;
        if (!uploadContext) {
            // 解析 multipart/form-data 边界
            std::string contentType = req.getHeader("Content-Type");
            if (contentType.empty()) {
                sendError(resp, "Content-Type header is missing", HttpResponse::k400BadRequest, conn);
                return true;
            }
            
            std::regex boundaryRegex("boundary=(.+)$");
            std::smatch matches;
            if (!std::regex_search(contentType, matches, boundaryRegex)) {
                sendError(resp, "Invalid Content-Type", HttpResponse::k400BadRequest, conn);
                return true;
            }
            boundary = "--" + matches[1].str();
            LOG_INFO << "Boundary: " << boundary;

            try {
                // 获取原始文件名
                //开始查找文件名
                // uploadContext->setExpectFileTypeState(FileUploadContext::FileTypeState::kFileName);
                // 1. 解析文件名
                size_t fileStart = body.find(boundary, currentPos);
                if (fileStart == std::string::npos) {
                    sendError(resp, "File boundary not found", HttpResponse::k400BadRequest, conn);
                    return true;
                }
                std::string originalFilename;
                currentPos = fileStart + boundary.length();
                std::regex filenameRegex("Content-Disposition:.*filename=\"([^\"]+)\"");
                if (std::regex_search(body, matches, filenameRegex) && matches[1].matched) {
                    originalFilename = matches[1].str();
                    LOG_INFO << "Got filename from Content-Disposition: " << originalFilename;
                } else {
                    originalFilename = "unknown_file";
                    LOG_INFO << "Using default filename: " << originalFilename;
                }

                 LOG_INFO << "Found filename: " << originalFilename;

 
                // 创建上传上下文
                uploadContext = std::make_shared<FileUploadContext>(originalFilename);
                if(uploadContext->connect()) {
                    LOG_INFO << "connect to fastdfs success";
                } else {
                    LOG_ERROR << "connect to fastdfs failed";
                    sendError(resp, "Failed to connect to FastDFS", HttpResponse::k500InternalServerError, conn);
                    return true;
                }
                
                httpContext->setContext(uploadContext);
                // 设置边界
                uploadContext->setBoundary(boundary);

                // 开始查找文件的内容
                uploadContext->setExpectFileTypeState(FileUploadContext::FileTypeState::kFileBody);
                // 解析body中的文件内容
                currentPos = body.find("\r\n\r\n", currentPos);
                // 跳过头部信息，获取文件内容
                currentPos += 4;
               
                LOG_INFO << "Created upload context for file: " << uploadContext->getOriginalFilename();
            } catch (const std::exception& e) {
                LOG_ERROR << "Failed to create upload context: " << e.what();
                sendError(resp, "Failed to create file", HttpResponse::k500InternalServerError, conn);
                return true;
            }
        }  
        boundary = uploadContext->getBoundary();
        while (currentPos < bodyEndPos)
        {
            bool break_while = false;
            switch (uploadContext->getFileTypeState())
            {
            case FileUploadContext::FileTypeState::kFileBody:
                LOG_INFO << "处理kFileBody";     
                nextBoundary = body.find(boundary, currentPos); 
                if (nextBoundary != std::string::npos) {
                        LOG_INFO << "currentPos: " << currentPos << ", nextBoundary: " << nextBoundary;
                    // 说明文件内容已经解析完毕 (nextBoundary -2) 减去换行符 才是真正的文件内容
                    uploadContext->uploadBlock(body.data() + currentPos, (nextBoundary -2) - currentPos);
                    currentPos = nextBoundary + boundary.length();
                    //更新下一个状态
                        uploadContext->setExpectFileTypeState(FileUploadContext::FileTypeState::kFileUser);
                } else {
                    uploadContext->uploadBlock(body.data() + currentPos, body.size() - currentPos);
                    currentPos = bodyEndPos;
                    break_while = true;
                }  
            break;
            case FileUploadContext::FileTypeState::kFileUser:
            {
                // 3. 解析 user
                std::string userField = "name=\"user\"";
                size_t userStart = body.find(userField, currentPos);
                if (userStart != std::string::npos) {
                    userStart = body.find("\r\n\r\n", userStart);
                    if (userStart != std::string::npos) {
                        userStart += 4;  // 跳过 \r\n\r\n
                        size_t userEnd = body.find("\r\n", userStart);
                        if (userEnd != std::string::npos) {
                            std::string user = body.substr(userStart, userEnd - userStart);
                            LOG_INFO << "Found user: " << user;
                            uploadContext->setUser(user);
                            currentPos = userEnd + 2;  // 跳过换行符
                            uploadContext->setExpectFileTypeState(FileUploadContext::FileTypeState::kFileMd5);
                        }
                    }
                } else {
                    break_while = true;
                }
            }    
            break;
            case FileUploadContext::FileTypeState::kFileMd5:
            {    // 4. 解析 md5
                std::string md5Field = "name=\"md5\"";
                size_t md5Start = body.find(md5Field, currentPos);
                if (md5Start != std::string::npos) {
                    md5Start = body.find("\r\n\r\n", md5Start);
                    if (md5Start != std::string::npos) {
                        md5Start += 4;  // 跳过 \r\n\r\n
                        size_t md5End = body.find("\r\n", md5Start);
                        if (md5End != std::string::npos) {
                            std::string md5 = body.substr(md5Start, md5End - md5Start);
                            LOG_INFO << "Found md5: " << md5;
                            uploadContext->setMd5(md5);
                            currentPos = md5End + 2;  // 跳过换行符
                            uploadContext->setExpectFileTypeState(FileUploadContext::FileTypeState::kFileSize);

                        }
                    }
                }else {
                    break_while = true;
                }
            }
                break;
                case FileUploadContext::FileTypeState::kFileSize:
                {
                // 5. 解析 size
                std::string sizeField = "name=\"size\"";
                size_t sizeStart = body.find(sizeField, currentPos);
                if (sizeStart != std::string::npos) {
                    sizeStart = body.find("\r\n\r\n", sizeStart);
                    if (sizeStart != std::string::npos) {
                        sizeStart += 4;  // 跳过 \r\n\r\n
                        size_t sizeEnd = body.find("\r\n", sizeStart);
                        if (sizeEnd != std::string::npos) {
                            std::string sizeStr = body.substr(sizeStart, sizeEnd - sizeStart);
                            LOG_INFO << "Found size: " << sizeStr;
                            uploadContext->setSize(std::stoull(sizeStr));
                            uploadContext->setExpectFileTypeState(
                                FileUploadContext::FileTypeState::kFileComplete);
                            currentPos = sizeEnd + 2;                           
                            LOG_INFO << "currentPos: " << currentPos;
                            // 跳过边界------WebKitFormBoundaryMlKbOVq1TUAbtOBd--
                            currentPos += boundary.length() + 2;
                            currentPos += 2; //换行符\r\n
                            LOG_INFO << "currentPos: " << currentPos;
                        }
                    }
                } else {
                    break_while = true;
                }
                }
                    
                break;
                default:
                break;
            }        
            if(break_while ) {
                LOG_INFO   << "break while";
            }
        }
        LOG_INFO << "currentPos: " << currentPos << ", bodyEndPos: " << bodyEndPos;
        req.setBody(""); // 清空请求体

        // 检查是否完成
        if (uploadContext->getFileTypeState() == FileUploadContext::FileTypeState::kFileComplete || httpContext->gotAll()) {
            LOG_INFO << "uploadContext->getFileId() = " << uploadContext->getFileId();
            LOG_INFO << "uploadContext->getOriginalFilename() = " << uploadContext->getOriginalFilename();

            FileInfo file_info;
            file_info.file_id = uploadContext->getFileId(); // 文件id
            file_info.file_name = uploadContext->getOriginalFilename(); // 文件名
            file_info.file_md5 = uploadContext->getMd5(); // 文件md5
            file_info.file_size = uploadContext->getSize(); // 文件大小
            file_info.file_type = uploadContext->getFileType(); // 文件类型
            file_info.file_url = uploadContext->getFileUrl(); // 文件url  可以加载的路径
            file_info.file_user = uploadContext->getUser();
            
            string resp_json;
            //把信息存储到数据库中
            int ret = ApiUploadFile(file_info, resp_json);
            setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
            return true;
        } else {
            LOG_INFO << "Waiting for more data, current state: " 
                     << static_cast<int>(uploadContext->getFileTypeState());
            return false;
        }
    }
 
    bool handleNotFound(const TcpConnectionPtr& conn, HttpResponse* resp) {

        string resp_json = "{\"message\": \"Not Found\"}"; 
        //设置响应
        setHttpResponse(resp, HttpResponse::k404NotFound, "Not Found", resp_json);
    
        return true;
    }


    // 账号注册处理
    bool  handleRegisterRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
        // 获取body
        string post_data =  req.body();  
        string resp_json;
        int ret = ApiRegisterUser(post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);

        return true;
    }

    bool handleLoginRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp)
    {
         // 获取body
        string post_data =  req.body();  
        string resp_json;
        int ret = ApiUserLogin(post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);

        return true;
    }

    bool handleMd5Request(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp)
    {
         // 获取body
        string post_data =  req.body();  
        string resp_json;
        int ret = ApiMd5(post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);

        return true;
    }

    bool handleMyFilesRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) 
    {
         // 获取body
        string post_data =  req.body();  
        string resp_json;
        string query = req.query();
        int ret = ApiMyfiles(query, post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
        return true;
    }
    
    bool handleSharepictureRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) 
    {

         // 获取body
        string post_data =  req.body();  
        string resp_json;
        string query = req.query();
        int ret = ApiSharepicture(query, post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
        return true;
    }
 
    bool handleDealfileRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
         // 获取body
        string post_data =  req.body();  
        string resp_json;
        string query = req.query();
        int ret = ApiDealfile(query, post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
        return true;
    }


    bool handleSharefilesRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
        // 获取body
        string post_data =  req.body();  
        string resp_json;
        string query = req.query();
        int ret = ApiSharefiles(query, post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
        return true;
    }


    bool handleDealsharefileRequest(const TcpConnectionPtr& conn, HttpRequest& req, HttpResponse* resp) {
        // 获取body
        string post_data =  req.body();  
        string resp_json;
        string query = req.query();
        int ret = ApiDealsharefile(query, post_data, resp_json);
        setHttpResponse(resp, HttpResponse::k200Ok, "OK", resp_json);
        return true;
    }


    void sendError(HttpResponse* resp, const std::string& message, 
                  HttpResponse::HttpStatusCode code, const TcpConnectionPtr& conn) {
        string resp_json = "{\"message\": \"" + message + "\"}"; 

        setHttpResponse(resp, code, message, resp_json);

        if (conn) {
            conn->setWriteCompleteCallback([conn](const TcpConnectionPtr& connection) {
                connection->shutdown();
                return true;
            });
        }
    }
 
    
private:
            
    void initRoutes() {
        addRoute("/api/upload", HttpRequest::kPost, &HttpHandler::handleFileUpload);
        addRoute("/api/reg", HttpRequest::kPost, &HttpHandler::handleRegisterRequest);
        addRoute("/api/login", HttpRequest::kPost, &HttpHandler::handleLoginRequest);
        addRoute("/api/md5", HttpRequest::kPost, &HttpHandler::handleMd5Request);
        addRoute("/api/myfiles", HttpRequest::kPost, &HttpHandler::handleMyFilesRequest);
        addRoute("/api/sharepic", HttpRequest::kPost, &HttpHandler::handleSharepictureRequest);
        addRoute("/api/dealfile", HttpRequest::kPost, &HttpHandler::handleDealfileRequest);
        addRoute("/api/sharefiles", HttpRequest::kPost, &HttpHandler::handleSharefilesRequest);
        addRoute("/api/sharefiles", HttpRequest::kGet, &HttpHandler::handleSharefilesRequest);
        addRoute("/api/dealsharefile", HttpRequest::kPost, &HttpHandler::handleDealsharefileRequest);
    }

    // 添加精确匹配的路由
    void addRoute(const std::string& path, HttpRequest::Method method, RequestHandler handler) {
        std::string pattern = "^" + escapeRegex(path) + "$";
        routes_.emplace_back(pattern, std::vector<std::string>(), handler, method);
    }

    // 添加带参数的路由
    void addRoute(const std::string& pattern, HttpRequest::Method method, 
                 RequestHandler handler, const std::vector<std::string>& paramNames) {
        routes_.emplace_back(pattern, paramNames, handler, method);
    }

    // 转义正则表达式特殊字符
    std::string escapeRegex(const std::string& str) {
        std::string result;
        for (char c : str) {
            if (c == '.' || c == '+' || c == '*' || c == '?' || c == '^' || 
                c == '$' || c == '(' || c == ')' || c == '[' || c == ']' || 
                c == '{' || c == '}' || c == '|' || c == '\\') {
                result += '\\';
            }
            result += c;
        }
        return result;
    }

    // SHA256 哈希算法简单实现
    std::string sha256(const std::string& input) {
        // 这只是一个简单的哈希表示，不是真正的SHA256
        // 在生产环境中，应使用专业的密码学库
        std::hash<std::string> hasher;
        auto hash = hasher(input);
        std::stringstream ss;
        ss << std::hex << hash;
        return ss.str();
    }
    #endif
};

#endif

#ifndef _APIUPLOAD_H_
#define _APIUPLOAD_H_
#include "api_common.h"

typedef struct _fileInfo {
    string file_id = "  ";  // 文件id
    string file_name = ""; // 文件名
    string file_md5 = ""; // 文件md5
    uint64_t file_size = 0; // 文件大小
    string file_type = ""; // 文件类型
    string file_url = ""; // 文件url  可以加载的路径
    string file_user = ""; // 文件用户
}FileInfo;

// 上传文件
int ApiUploadFile(FileInfo &file_info, string &resp_json);
#endif // !__UPLOAD_H_
 代码对应：tc_http_srv7 做了加锁的操作修改，大家可以根据client/2-upload的代码 写一个客户端多线程的测试程序：

1. 清空0voice_tuchuang的记录，并注册用户1/用户2/用户3/用户4/用户5  五个用户。
2. 用户1/用户2/用户3/用户4/用户5   多线程上传 进行文件上传测试， 准备 10个不同文件内容的文件，文件不用太大，比如10字节即可，但10个文件的内容不能相同，反复进行上传测试。 文件名可以为1.txt 2.txt 3.txt 4.txt 5.txt 6.txt 7.txt 8.txt 9.txt 10.txt。

​	文件内容分别为: 1 2 3 4 5 6 7 8 9 10，我已经放到**tc-src/client/3-upload/test_files 目录。**

3. 用户1/用户2/用户3/用户4/用户5   进行多线程 删除文件测试（都删除 10个不同的文件），反复进行删除测试。

4. 最后先退出文件上传测试， 然后再退出文件删除测试， 最后分析 file_info的记录是否被删除，分析fastdfs对应的文件是否被删除。


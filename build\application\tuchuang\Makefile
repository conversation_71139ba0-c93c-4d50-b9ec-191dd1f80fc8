# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\application\tuchuang\\CMakeFiles\progress.marks
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
application/tuchuang/CMakeFiles/tc_http_server.dir/rule:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 application/tuchuang/CMakeFiles/tc_http_server.dir/rule
.PHONY : application/tuchuang/CMakeFiles/tc_http_server.dir/rule

# Convenience name for target.
tc_http_server: application/tuchuang/CMakeFiles/tc_http_server.dir/rule
.PHONY : tc_http_server

# fast build rule for target.
tc_http_server/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/build
.PHONY : tc_http_server/fast

api/api_common.obj: api/api_common.cc.obj
.PHONY : api/api_common.obj

# target to build an object file
api/api_common.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.obj
.PHONY : api/api_common.cc.obj

api/api_common.i: api/api_common.cc.i
.PHONY : api/api_common.i

# target to preprocess a source file
api/api_common.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.i
.PHONY : api/api_common.cc.i

api/api_common.s: api/api_common.cc.s
.PHONY : api/api_common.s

# target to generate assembly for a file
api/api_common.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_common.cc.s
.PHONY : api/api_common.cc.s

api/api_deal_sharefile.obj: api/api_deal_sharefile.cc.obj
.PHONY : api/api_deal_sharefile.obj

# target to build an object file
api/api_deal_sharefile.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.obj
.PHONY : api/api_deal_sharefile.cc.obj

api/api_deal_sharefile.i: api/api_deal_sharefile.cc.i
.PHONY : api/api_deal_sharefile.i

# target to preprocess a source file
api/api_deal_sharefile.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.i
.PHONY : api/api_deal_sharefile.cc.i

api/api_deal_sharefile.s: api/api_deal_sharefile.cc.s
.PHONY : api/api_deal_sharefile.s

# target to generate assembly for a file
api/api_deal_sharefile.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_deal_sharefile.cc.s
.PHONY : api/api_deal_sharefile.cc.s

api/api_dealfile.obj: api/api_dealfile.cc.obj
.PHONY : api/api_dealfile.obj

# target to build an object file
api/api_dealfile.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.obj
.PHONY : api/api_dealfile.cc.obj

api/api_dealfile.i: api/api_dealfile.cc.i
.PHONY : api/api_dealfile.i

# target to preprocess a source file
api/api_dealfile.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.i
.PHONY : api/api_dealfile.cc.i

api/api_dealfile.s: api/api_dealfile.cc.s
.PHONY : api/api_dealfile.s

# target to generate assembly for a file
api/api_dealfile.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_dealfile.cc.s
.PHONY : api/api_dealfile.cc.s

api/api_login.obj: api/api_login.cc.obj
.PHONY : api/api_login.obj

# target to build an object file
api/api_login.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.obj
.PHONY : api/api_login.cc.obj

api/api_login.i: api/api_login.cc.i
.PHONY : api/api_login.i

# target to preprocess a source file
api/api_login.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.i
.PHONY : api/api_login.cc.i

api/api_login.s: api/api_login.cc.s
.PHONY : api/api_login.s

# target to generate assembly for a file
api/api_login.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_login.cc.s
.PHONY : api/api_login.cc.s

api/api_md5.obj: api/api_md5.cc.obj
.PHONY : api/api_md5.obj

# target to build an object file
api/api_md5.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.obj
.PHONY : api/api_md5.cc.obj

api/api_md5.i: api/api_md5.cc.i
.PHONY : api/api_md5.i

# target to preprocess a source file
api/api_md5.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.i
.PHONY : api/api_md5.cc.i

api/api_md5.s: api/api_md5.cc.s
.PHONY : api/api_md5.s

# target to generate assembly for a file
api/api_md5.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_md5.cc.s
.PHONY : api/api_md5.cc.s

api/api_myfiles.obj: api/api_myfiles.cc.obj
.PHONY : api/api_myfiles.obj

# target to build an object file
api/api_myfiles.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.obj
.PHONY : api/api_myfiles.cc.obj

api/api_myfiles.i: api/api_myfiles.cc.i
.PHONY : api/api_myfiles.i

# target to preprocess a source file
api/api_myfiles.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.i
.PHONY : api/api_myfiles.cc.i

api/api_myfiles.s: api/api_myfiles.cc.s
.PHONY : api/api_myfiles.s

# target to generate assembly for a file
api/api_myfiles.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_myfiles.cc.s
.PHONY : api/api_myfiles.cc.s

api/api_register.obj: api/api_register.cc.obj
.PHONY : api/api_register.obj

# target to build an object file
api/api_register.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.obj
.PHONY : api/api_register.cc.obj

api/api_register.i: api/api_register.cc.i
.PHONY : api/api_register.i

# target to preprocess a source file
api/api_register.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.i
.PHONY : api/api_register.cc.i

api/api_register.s: api/api_register.cc.s
.PHONY : api/api_register.s

# target to generate assembly for a file
api/api_register.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_register.cc.s
.PHONY : api/api_register.cc.s

api/api_sharefiles.obj: api/api_sharefiles.cc.obj
.PHONY : api/api_sharefiles.obj

# target to build an object file
api/api_sharefiles.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.obj
.PHONY : api/api_sharefiles.cc.obj

api/api_sharefiles.i: api/api_sharefiles.cc.i
.PHONY : api/api_sharefiles.i

# target to preprocess a source file
api/api_sharefiles.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.i
.PHONY : api/api_sharefiles.cc.i

api/api_sharefiles.s: api/api_sharefiles.cc.s
.PHONY : api/api_sharefiles.s

# target to generate assembly for a file
api/api_sharefiles.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharefiles.cc.s
.PHONY : api/api_sharefiles.cc.s

api/api_sharepicture.obj: api/api_sharepicture.cc.obj
.PHONY : api/api_sharepicture.obj

# target to build an object file
api/api_sharepicture.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.obj
.PHONY : api/api_sharepicture.cc.obj

api/api_sharepicture.i: api/api_sharepicture.cc.i
.PHONY : api/api_sharepicture.i

# target to preprocess a source file
api/api_sharepicture.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.i
.PHONY : api/api_sharepicture.cc.i

api/api_sharepicture.s: api/api_sharepicture.cc.s
.PHONY : api/api_sharepicture.s

# target to generate assembly for a file
api/api_sharepicture.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_sharepicture.cc.s
.PHONY : api/api_sharepicture.cc.s

api/api_upload.obj: api/api_upload.cc.obj
.PHONY : api/api_upload.obj

# target to build an object file
api/api_upload.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.obj
.PHONY : api/api_upload.cc.obj

api/api_upload.i: api/api_upload.cc.i
.PHONY : api/api_upload.i

# target to preprocess a source file
api/api_upload.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.i
.PHONY : api/api_upload.cc.i

api/api_upload.s: api/api_upload.cc.s
.PHONY : api/api_upload.s

# target to generate assembly for a file
api/api_upload.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/api/api_upload.cc.s
.PHONY : api/api_upload.cc.s

base/config_file_reader.obj: base/config_file_reader.cc.obj
.PHONY : base/config_file_reader.obj

# target to build an object file
base/config_file_reader.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.obj
.PHONY : base/config_file_reader.cc.obj

base/config_file_reader.i: base/config_file_reader.cc.i
.PHONY : base/config_file_reader.i

# target to preprocess a source file
base/config_file_reader.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.i
.PHONY : base/config_file_reader.cc.i

base/config_file_reader.s: base/config_file_reader.cc.s
.PHONY : base/config_file_reader.s

# target to generate assembly for a file
base/config_file_reader.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/config_file_reader.cc.s
.PHONY : base/config_file_reader.cc.s

base/tc_common.obj: base/tc_common.cc.obj
.PHONY : base/tc_common.obj

# target to build an object file
base/tc_common.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.obj
.PHONY : base/tc_common.cc.obj

base/tc_common.i: base/tc_common.cc.i
.PHONY : base/tc_common.i

# target to preprocess a source file
base/tc_common.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.i
.PHONY : base/tc_common.cc.i

base/tc_common.s: base/tc_common.cc.s
.PHONY : base/tc_common.s

# target to generate assembly for a file
base/tc_common.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/tc_common.cc.s
.PHONY : base/tc_common.cc.s

base/util.obj: base/util.cc.obj
.PHONY : base/util.obj

# target to build an object file
base/util.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.obj
.PHONY : base/util.cc.obj

base/util.i: base/util.cc.i
.PHONY : base/util.i

# target to preprocess a source file
base/util.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.i
.PHONY : base/util.cc.i

base/util.s: base/util.cc.s
.PHONY : base/util.s

# target to generate assembly for a file
base/util.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/base/util.cc.s
.PHONY : base/util.cc.s

http_conn.obj: http_conn.cc.obj
.PHONY : http_conn.obj

# target to build an object file
http_conn.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.obj
.PHONY : http_conn.cc.obj

http_conn.i: http_conn.cc.i
.PHONY : http_conn.i

# target to preprocess a source file
http_conn.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.i
.PHONY : http_conn.cc.i

http_conn.s: http_conn.cc.s
.PHONY : http_conn.s

# target to generate assembly for a file
http_conn.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_conn.cc.s
.PHONY : http_conn.cc.s

http_parser.obj: http_parser.cc.obj
.PHONY : http_parser.obj

# target to build an object file
http_parser.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.obj
.PHONY : http_parser.cc.obj

http_parser.i: http_parser.cc.i
.PHONY : http_parser.i

# target to preprocess a source file
http_parser.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.i
.PHONY : http_parser.cc.i

http_parser.s: http_parser.cc.s
.PHONY : http_parser.s

# target to generate assembly for a file
http_parser.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser.cc.s
.PHONY : http_parser.cc.s

http_parser_wrapper.obj: http_parser_wrapper.cc.obj
.PHONY : http_parser_wrapper.obj

# target to build an object file
http_parser_wrapper.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.obj
.PHONY : http_parser_wrapper.cc.obj

http_parser_wrapper.i: http_parser_wrapper.cc.i
.PHONY : http_parser_wrapper.i

# target to preprocess a source file
http_parser_wrapper.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.i
.PHONY : http_parser_wrapper.cc.i

http_parser_wrapper.s: http_parser_wrapper.cc.s
.PHONY : http_parser_wrapper.s

# target to generate assembly for a file
http_parser_wrapper.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/http_parser_wrapper.cc.s
.PHONY : http_parser_wrapper.cc.s

jsoncpp/json_reader.obj: jsoncpp/json_reader.cpp.obj
.PHONY : jsoncpp/json_reader.obj

# target to build an object file
jsoncpp/json_reader.cpp.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.obj
.PHONY : jsoncpp/json_reader.cpp.obj

jsoncpp/json_reader.i: jsoncpp/json_reader.cpp.i
.PHONY : jsoncpp/json_reader.i

# target to preprocess a source file
jsoncpp/json_reader.cpp.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.i
.PHONY : jsoncpp/json_reader.cpp.i

jsoncpp/json_reader.s: jsoncpp/json_reader.cpp.s
.PHONY : jsoncpp/json_reader.s

# target to generate assembly for a file
jsoncpp/json_reader.cpp.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_reader.cpp.s
.PHONY : jsoncpp/json_reader.cpp.s

jsoncpp/json_value.obj: jsoncpp/json_value.cpp.obj
.PHONY : jsoncpp/json_value.obj

# target to build an object file
jsoncpp/json_value.cpp.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.obj
.PHONY : jsoncpp/json_value.cpp.obj

jsoncpp/json_value.i: jsoncpp/json_value.cpp.i
.PHONY : jsoncpp/json_value.i

# target to preprocess a source file
jsoncpp/json_value.cpp.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.i
.PHONY : jsoncpp/json_value.cpp.i

jsoncpp/json_value.s: jsoncpp/json_value.cpp.s
.PHONY : jsoncpp/json_value.s

# target to generate assembly for a file
jsoncpp/json_value.cpp.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_value.cpp.s
.PHONY : jsoncpp/json_value.cpp.s

jsoncpp/json_writer.obj: jsoncpp/json_writer.cpp.obj
.PHONY : jsoncpp/json_writer.obj

# target to build an object file
jsoncpp/json_writer.cpp.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.obj
.PHONY : jsoncpp/json_writer.cpp.obj

jsoncpp/json_writer.i: jsoncpp/json_writer.cpp.i
.PHONY : jsoncpp/json_writer.i

# target to preprocess a source file
jsoncpp/json_writer.cpp.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.i
.PHONY : jsoncpp/json_writer.cpp.i

jsoncpp/json_writer.s: jsoncpp/json_writer.cpp.s
.PHONY : jsoncpp/json_writer.s

# target to generate assembly for a file
jsoncpp/json_writer.cpp.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/jsoncpp/json_writer.cpp.s
.PHONY : jsoncpp/json_writer.cpp.s

main.obj: main.cc.obj
.PHONY : main.obj

# target to build an object file
main.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.obj
.PHONY : main.cc.obj

main.i: main.cc.i
.PHONY : main.i

# target to preprocess a source file
main.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.i
.PHONY : main.cc.i

main.s: main.cc.s
.PHONY : main.s

# target to generate assembly for a file
main.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/main.cc.s
.PHONY : main.cc.s

mysql/db_pool.obj: mysql/db_pool.cc.obj
.PHONY : mysql/db_pool.obj

# target to build an object file
mysql/db_pool.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.obj
.PHONY : mysql/db_pool.cc.obj

mysql/db_pool.i: mysql/db_pool.cc.i
.PHONY : mysql/db_pool.i

# target to preprocess a source file
mysql/db_pool.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.i
.PHONY : mysql/db_pool.cc.i

mysql/db_pool.s: mysql/db_pool.cc.s
.PHONY : mysql/db_pool.s

# target to generate assembly for a file
mysql/db_pool.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/mysql/db_pool.cc.s
.PHONY : mysql/db_pool.cc.s

redis/async.obj: redis/async.c.obj
.PHONY : redis/async.obj

# target to build an object file
redis/async.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.obj
.PHONY : redis/async.c.obj

redis/async.i: redis/async.c.i
.PHONY : redis/async.i

# target to preprocess a source file
redis/async.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.i
.PHONY : redis/async.c.i

redis/async.s: redis/async.c.s
.PHONY : redis/async.s

# target to generate assembly for a file
redis/async.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/async.c.s
.PHONY : redis/async.c.s

redis/cache_pool.obj: redis/cache_pool.cc.obj
.PHONY : redis/cache_pool.obj

# target to build an object file
redis/cache_pool.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.obj
.PHONY : redis/cache_pool.cc.obj

redis/cache_pool.i: redis/cache_pool.cc.i
.PHONY : redis/cache_pool.i

# target to preprocess a source file
redis/cache_pool.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.i
.PHONY : redis/cache_pool.cc.i

redis/cache_pool.s: redis/cache_pool.cc.s
.PHONY : redis/cache_pool.s

# target to generate assembly for a file
redis/cache_pool.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/cache_pool.cc.s
.PHONY : redis/cache_pool.cc.s

redis/dict.obj: redis/dict.c.obj
.PHONY : redis/dict.obj

# target to build an object file
redis/dict.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.obj
.PHONY : redis/dict.c.obj

redis/dict.i: redis/dict.c.i
.PHONY : redis/dict.i

# target to preprocess a source file
redis/dict.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.i
.PHONY : redis/dict.c.i

redis/dict.s: redis/dict.c.s
.PHONY : redis/dict.s

# target to generate assembly for a file
redis/dict.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/dict.c.s
.PHONY : redis/dict.c.s

redis/hiredis.obj: redis/hiredis.c.obj
.PHONY : redis/hiredis.obj

# target to build an object file
redis/hiredis.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.obj
.PHONY : redis/hiredis.c.obj

redis/hiredis.i: redis/hiredis.c.i
.PHONY : redis/hiredis.i

# target to preprocess a source file
redis/hiredis.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.i
.PHONY : redis/hiredis.c.i

redis/hiredis.s: redis/hiredis.c.s
.PHONY : redis/hiredis.s

# target to generate assembly for a file
redis/hiredis.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/hiredis.c.s
.PHONY : redis/hiredis.c.s

redis/net.obj: redis/net.c.obj
.PHONY : redis/net.obj

# target to build an object file
redis/net.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.obj
.PHONY : redis/net.c.obj

redis/net.i: redis/net.c.i
.PHONY : redis/net.i

# target to preprocess a source file
redis/net.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.i
.PHONY : redis/net.c.i

redis/net.s: redis/net.c.s
.PHONY : redis/net.s

# target to generate assembly for a file
redis/net.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/net.c.s
.PHONY : redis/net.c.s

redis/read.obj: redis/read.c.obj
.PHONY : redis/read.obj

# target to build an object file
redis/read.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.obj
.PHONY : redis/read.c.obj

redis/read.i: redis/read.c.i
.PHONY : redis/read.i

# target to preprocess a source file
redis/read.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.i
.PHONY : redis/read.c.i

redis/read.s: redis/read.c.s
.PHONY : redis/read.s

# target to generate assembly for a file
redis/read.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/read.c.s
.PHONY : redis/read.c.s

redis/sds.obj: redis/sds.c.obj
.PHONY : redis/sds.obj

# target to build an object file
redis/sds.c.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.obj
.PHONY : redis/sds.c.obj

redis/sds.i: redis/sds.c.i
.PHONY : redis/sds.i

# target to preprocess a source file
redis/sds.c.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.i
.PHONY : redis/sds.c.i

redis/sds.s: redis/sds.c.s
.PHONY : redis/sds.s

# target to generate assembly for a file
redis/sds.c.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f application\tuchuang\CMakeFiles\tc_http_server.dir\build.make application/tuchuang/CMakeFiles/tc_http_server.dir/redis/sds.c.s
.PHONY : redis/sds.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... tc_http_server
	@echo ... api/api_common.obj
	@echo ... api/api_common.i
	@echo ... api/api_common.s
	@echo ... api/api_deal_sharefile.obj
	@echo ... api/api_deal_sharefile.i
	@echo ... api/api_deal_sharefile.s
	@echo ... api/api_dealfile.obj
	@echo ... api/api_dealfile.i
	@echo ... api/api_dealfile.s
	@echo ... api/api_login.obj
	@echo ... api/api_login.i
	@echo ... api/api_login.s
	@echo ... api/api_md5.obj
	@echo ... api/api_md5.i
	@echo ... api/api_md5.s
	@echo ... api/api_myfiles.obj
	@echo ... api/api_myfiles.i
	@echo ... api/api_myfiles.s
	@echo ... api/api_register.obj
	@echo ... api/api_register.i
	@echo ... api/api_register.s
	@echo ... api/api_sharefiles.obj
	@echo ... api/api_sharefiles.i
	@echo ... api/api_sharefiles.s
	@echo ... api/api_sharepicture.obj
	@echo ... api/api_sharepicture.i
	@echo ... api/api_sharepicture.s
	@echo ... api/api_upload.obj
	@echo ... api/api_upload.i
	@echo ... api/api_upload.s
	@echo ... base/config_file_reader.obj
	@echo ... base/config_file_reader.i
	@echo ... base/config_file_reader.s
	@echo ... base/tc_common.obj
	@echo ... base/tc_common.i
	@echo ... base/tc_common.s
	@echo ... base/util.obj
	@echo ... base/util.i
	@echo ... base/util.s
	@echo ... http_conn.obj
	@echo ... http_conn.i
	@echo ... http_conn.s
	@echo ... http_parser.obj
	@echo ... http_parser.i
	@echo ... http_parser.s
	@echo ... http_parser_wrapper.obj
	@echo ... http_parser_wrapper.i
	@echo ... http_parser_wrapper.s
	@echo ... jsoncpp/json_reader.obj
	@echo ... jsoncpp/json_reader.i
	@echo ... jsoncpp/json_reader.s
	@echo ... jsoncpp/json_value.obj
	@echo ... jsoncpp/json_value.i
	@echo ... jsoncpp/json_value.s
	@echo ... jsoncpp/json_writer.obj
	@echo ... jsoncpp/json_writer.i
	@echo ... jsoncpp/json_writer.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... mysql/db_pool.obj
	@echo ... mysql/db_pool.i
	@echo ... mysql/db_pool.s
	@echo ... redis/async.obj
	@echo ... redis/async.i
	@echo ... redis/async.s
	@echo ... redis/cache_pool.obj
	@echo ... redis/cache_pool.i
	@echo ... redis/cache_pool.s
	@echo ... redis/dict.obj
	@echo ... redis/dict.i
	@echo ... redis/dict.s
	@echo ... redis/hiredis.obj
	@echo ... redis/hiredis.i
	@echo ... redis/hiredis.s
	@echo ... redis/net.obj
	@echo ... redis/net.i
	@echo ... redis/net.s
	@echo ... redis/read.obj
	@echo ... redis/read.i
	@echo ... redis/read.s
	@echo ... redis/sds.obj
	@echo ... redis/sds.i
	@echo ... redis/sds.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


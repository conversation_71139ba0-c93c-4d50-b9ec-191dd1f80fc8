{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20"}, "version": {"isDirty": false, "major": 3, "minor": 20, "patch": 21032501, "string": "3.20.21032501-MSVC_2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-5ded271ac525a81080cd.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "cache-v2-b3933b6cb42c3e1d7d35.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-aef772ee1c4322fd5a8a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-71789b856437a5573594.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-b3933b6cb42c3e1d7d35.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-5ded271ac525a81080cd.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "toolchains-v1-71789b856437a5573594.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-aef772ee1c4322fd5a8a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}
# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\muduo\base\\CMakeFiles\progress.marks
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
muduo/base/CMakeFiles/muduo_base.dir/rule:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 muduo/base/CMakeFiles/muduo_base.dir/rule
.PHONY : muduo/base/CMakeFiles/muduo_base.dir/rule

# Convenience name for target.
muduo_base: muduo/base/CMakeFiles/muduo_base.dir/rule
.PHONY : muduo_base

# fast build rule for target.
muduo_base/fast:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/build
.PHONY : muduo_base/fast

AsyncLogging.obj: AsyncLogging.cc.obj
.PHONY : AsyncLogging.obj

# target to build an object file
AsyncLogging.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.obj
.PHONY : AsyncLogging.cc.obj

AsyncLogging.i: AsyncLogging.cc.i
.PHONY : AsyncLogging.i

# target to preprocess a source file
AsyncLogging.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.i
.PHONY : AsyncLogging.cc.i

AsyncLogging.s: AsyncLogging.cc.s
.PHONY : AsyncLogging.s

# target to generate assembly for a file
AsyncLogging.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/AsyncLogging.cc.s
.PHONY : AsyncLogging.cc.s

Condition.obj: Condition.cc.obj
.PHONY : Condition.obj

# target to build an object file
Condition.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.obj
.PHONY : Condition.cc.obj

Condition.i: Condition.cc.i
.PHONY : Condition.i

# target to preprocess a source file
Condition.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.i
.PHONY : Condition.cc.i

Condition.s: Condition.cc.s
.PHONY : Condition.s

# target to generate assembly for a file
Condition.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Condition.cc.s
.PHONY : Condition.cc.s

CountDownLatch.obj: CountDownLatch.cc.obj
.PHONY : CountDownLatch.obj

# target to build an object file
CountDownLatch.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.obj
.PHONY : CountDownLatch.cc.obj

CountDownLatch.i: CountDownLatch.cc.i
.PHONY : CountDownLatch.i

# target to preprocess a source file
CountDownLatch.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.i
.PHONY : CountDownLatch.cc.i

CountDownLatch.s: CountDownLatch.cc.s
.PHONY : CountDownLatch.s

# target to generate assembly for a file
CountDownLatch.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CountDownLatch.cc.s
.PHONY : CountDownLatch.cc.s

CurrentThread.obj: CurrentThread.cc.obj
.PHONY : CurrentThread.obj

# target to build an object file
CurrentThread.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.obj
.PHONY : CurrentThread.cc.obj

CurrentThread.i: CurrentThread.cc.i
.PHONY : CurrentThread.i

# target to preprocess a source file
CurrentThread.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.i
.PHONY : CurrentThread.cc.i

CurrentThread.s: CurrentThread.cc.s
.PHONY : CurrentThread.s

# target to generate assembly for a file
CurrentThread.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/CurrentThread.cc.s
.PHONY : CurrentThread.cc.s

Date.obj: Date.cc.obj
.PHONY : Date.obj

# target to build an object file
Date.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Date.cc.obj
.PHONY : Date.cc.obj

Date.i: Date.cc.i
.PHONY : Date.i

# target to preprocess a source file
Date.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Date.cc.i
.PHONY : Date.cc.i

Date.s: Date.cc.s
.PHONY : Date.s

# target to generate assembly for a file
Date.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Date.cc.s
.PHONY : Date.cc.s

Exception.obj: Exception.cc.obj
.PHONY : Exception.obj

# target to build an object file
Exception.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.obj
.PHONY : Exception.cc.obj

Exception.i: Exception.cc.i
.PHONY : Exception.i

# target to preprocess a source file
Exception.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.i
.PHONY : Exception.cc.i

Exception.s: Exception.cc.s
.PHONY : Exception.s

# target to generate assembly for a file
Exception.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Exception.cc.s
.PHONY : Exception.cc.s

FileUtil.obj: FileUtil.cc.obj
.PHONY : FileUtil.obj

# target to build an object file
FileUtil.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.obj
.PHONY : FileUtil.cc.obj

FileUtil.i: FileUtil.cc.i
.PHONY : FileUtil.i

# target to preprocess a source file
FileUtil.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.i
.PHONY : FileUtil.cc.i

FileUtil.s: FileUtil.cc.s
.PHONY : FileUtil.s

# target to generate assembly for a file
FileUtil.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/FileUtil.cc.s
.PHONY : FileUtil.cc.s

LogFile.obj: LogFile.cc.obj
.PHONY : LogFile.obj

# target to build an object file
LogFile.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.obj
.PHONY : LogFile.cc.obj

LogFile.i: LogFile.cc.i
.PHONY : LogFile.i

# target to preprocess a source file
LogFile.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.i
.PHONY : LogFile.cc.i

LogFile.s: LogFile.cc.s
.PHONY : LogFile.s

# target to generate assembly for a file
LogFile.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogFile.cc.s
.PHONY : LogFile.cc.s

LogStream.obj: LogStream.cc.obj
.PHONY : LogStream.obj

# target to build an object file
LogStream.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.obj
.PHONY : LogStream.cc.obj

LogStream.i: LogStream.cc.i
.PHONY : LogStream.i

# target to preprocess a source file
LogStream.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.i
.PHONY : LogStream.cc.i

LogStream.s: LogStream.cc.s
.PHONY : LogStream.s

# target to generate assembly for a file
LogStream.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/LogStream.cc.s
.PHONY : LogStream.cc.s

Logging.obj: Logging.cc.obj
.PHONY : Logging.obj

# target to build an object file
Logging.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.obj
.PHONY : Logging.cc.obj

Logging.i: Logging.cc.i
.PHONY : Logging.i

# target to preprocess a source file
Logging.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.i
.PHONY : Logging.cc.i

Logging.s: Logging.cc.s
.PHONY : Logging.s

# target to generate assembly for a file
Logging.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Logging.cc.s
.PHONY : Logging.cc.s

ProcessInfo.obj: ProcessInfo.cc.obj
.PHONY : ProcessInfo.obj

# target to build an object file
ProcessInfo.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.obj
.PHONY : ProcessInfo.cc.obj

ProcessInfo.i: ProcessInfo.cc.i
.PHONY : ProcessInfo.i

# target to preprocess a source file
ProcessInfo.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.i
.PHONY : ProcessInfo.cc.i

ProcessInfo.s: ProcessInfo.cc.s
.PHONY : ProcessInfo.s

# target to generate assembly for a file
ProcessInfo.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ProcessInfo.cc.s
.PHONY : ProcessInfo.cc.s

Thread.obj: Thread.cc.obj
.PHONY : Thread.obj

# target to build an object file
Thread.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.obj
.PHONY : Thread.cc.obj

Thread.i: Thread.cc.i
.PHONY : Thread.i

# target to preprocess a source file
Thread.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.i
.PHONY : Thread.cc.i

Thread.s: Thread.cc.s
.PHONY : Thread.s

# target to generate assembly for a file
Thread.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Thread.cc.s
.PHONY : Thread.cc.s

ThreadPool.obj: ThreadPool.cc.obj
.PHONY : ThreadPool.obj

# target to build an object file
ThreadPool.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.obj
.PHONY : ThreadPool.cc.obj

ThreadPool.i: ThreadPool.cc.i
.PHONY : ThreadPool.i

# target to preprocess a source file
ThreadPool.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.i
.PHONY : ThreadPool.cc.i

ThreadPool.s: ThreadPool.cc.s
.PHONY : ThreadPool.s

# target to generate assembly for a file
ThreadPool.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/ThreadPool.cc.s
.PHONY : ThreadPool.cc.s

TimeZone.obj: TimeZone.cc.obj
.PHONY : TimeZone.obj

# target to build an object file
TimeZone.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.obj
.PHONY : TimeZone.cc.obj

TimeZone.i: TimeZone.cc.i
.PHONY : TimeZone.i

# target to preprocess a source file
TimeZone.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.i
.PHONY : TimeZone.cc.i

TimeZone.s: TimeZone.cc.s
.PHONY : TimeZone.s

# target to generate assembly for a file
TimeZone.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/TimeZone.cc.s
.PHONY : TimeZone.cc.s

Timestamp.obj: Timestamp.cc.obj
.PHONY : Timestamp.obj

# target to build an object file
Timestamp.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.obj
.PHONY : Timestamp.cc.obj

Timestamp.i: Timestamp.cc.i
.PHONY : Timestamp.i

# target to preprocess a source file
Timestamp.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.i
.PHONY : Timestamp.cc.i

Timestamp.s: Timestamp.cc.s
.PHONY : Timestamp.s

# target to generate assembly for a file
Timestamp.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/Timestamp.cc.s
.PHONY : Timestamp.cc.s

md5.obj: md5.cc.obj
.PHONY : md5.obj

# target to build an object file
md5.cc.obj:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/md5.cc.obj
.PHONY : md5.cc.obj

md5.i: md5.cc.i
.PHONY : md5.i

# target to preprocess a source file
md5.cc.i:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/md5.cc.i
.PHONY : md5.cc.i

md5.s: md5.cc.s
.PHONY : md5.s

# target to generate assembly for a file
md5.cc.s:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(MAKE) $(MAKESILENT) -f muduo\base\CMakeFiles\muduo_base.dir\build.make muduo/base/CMakeFiles/muduo_base.dir/md5.cc.s
.PHONY : md5.cc.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... muduo_base
	@echo ... AsyncLogging.obj
	@echo ... AsyncLogging.i
	@echo ... AsyncLogging.s
	@echo ... Condition.obj
	@echo ... Condition.i
	@echo ... Condition.s
	@echo ... CountDownLatch.obj
	@echo ... CountDownLatch.i
	@echo ... CountDownLatch.s
	@echo ... CurrentThread.obj
	@echo ... CurrentThread.i
	@echo ... CurrentThread.s
	@echo ... Date.obj
	@echo ... Date.i
	@echo ... Date.s
	@echo ... Exception.obj
	@echo ... Exception.i
	@echo ... Exception.s
	@echo ... FileUtil.obj
	@echo ... FileUtil.i
	@echo ... FileUtil.s
	@echo ... LogFile.obj
	@echo ... LogFile.i
	@echo ... LogFile.s
	@echo ... LogStream.obj
	@echo ... LogStream.i
	@echo ... LogStream.s
	@echo ... Logging.obj
	@echo ... Logging.i
	@echo ... Logging.s
	@echo ... ProcessInfo.obj
	@echo ... ProcessInfo.i
	@echo ... ProcessInfo.s
	@echo ... Thread.obj
	@echo ... Thread.i
	@echo ... Thread.s
	@echo ... ThreadPool.obj
	@echo ... ThreadPool.i
	@echo ... ThreadPool.s
	@echo ... TimeZone.obj
	@echo ... TimeZone.i
	@echo ... TimeZone.s
	@echo ... Timestamp.obj
	@echo ... Timestamp.i
	@echo ... Timestamp.s
	@echo ... md5.obj
	@echo ... md5.i
	@echo ... md5.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\Users\<USER>\Downloads\tuchuang-master\tuchuang-master\tc-src\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


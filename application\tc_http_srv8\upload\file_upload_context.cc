#include "file_upload_context.h"
#include <cstring>

extern "C" {
#include "fastcommon/shared_func.h"
#include "fastdfs/tracker_types.h"
#include "fastdfs/tracker_proto.h"
#include "fastdfs/tracker_client.h"
#include "fastdfs/storage_client.h"
#include "fastdfs/storage_client1.h"
#include "fastdfs/client_func.h"
#include "fastdfs/client_global.h"
#include "fastcommon/connection_pool.h"
}

#include "muduo/base/Logging.h"
using namespace muduo;


FileUploadContext::FileUploadContext(const std::string& originalFilename) 
    : tracker_server_(nullptr)
    , store_path_index_(0)
    , connected_(false)
    , originalFilename_(originalFilename) {
    group_name_[0] = '\0';

    // 获取文件扩展名
    size_t pos = originalFilename.find_last_of('.');
    if(pos != std::string::npos) {
        file_type_ = originalFilename.substr(pos + 1); //+1去掉 .
    }
    LOG_INFO2 << "FileUploadContext(), id = " << file_id_generator_.incrementAndGet();
}

FileUploadContext::~FileUploadContext() {
    disconnect();
    if(storage_server_) {
        free(storage_server_);
    }
    LOG_INFO2 << "~FileUploadContext(), id = " << file_id_generator_.get();
}

bool FileUploadContext::uploadBlock(const char* data, size_t size) {
    LOG_INFO << "size = " << size;
    if(is_first_block_) {
        return uploadFirstBlock(data, size);
    } else {
        return appendBlock(data, size);
    }
}

bool FileUploadContext::connect(const std::string& config_file) {
    if (connected_) {
        return true;
    }

    log_init();
    g_log_context.log_level = LOG_ERR;
    ignore_signal_pipe();

    int result = fdfs_client_init(config_file.c_str());
    if (result != 0) {
        return false;
    }

    tracker_server_ = tracker_get_connection();
    if (tracker_server_ == nullptr) {
        fdfs_client_destroy();
        return false;
    }

    storage_server_ = malloc(sizeof(ConnectionInfo));

    result = tracker_query_storage_store(static_cast<ConnectionInfo *>(tracker_server_),
            static_cast<ConnectionInfo *>(storage_server_), group_name_, &store_path_index_);
    if (result != 0) {
        fdfs_client_destroy();
        return false;
    }
    

    connected_ = true;
    return true;
}

bool FileUploadContext::uploadFirstBlock(const char* data, size_t size) {
    if (!connected_) {
        return false;
    }

    char file_id_buf[256];
    int result = storage_upload_appender_by_filebuff1(static_cast<ConnectionInfo *>(tracker_server_),
            static_cast<ConnectionInfo *>(storage_server_), store_path_index_,
            data, size, file_type_.c_str(),
            nullptr, 0, group_name_, file_id_buf);
    if (result == 0) {
        file_id_ = file_id_buf;
        string ip =  static_cast<ConnectionInfo *>(storage_server_)->ip_addr;
        // 拼接文件url
        file_url_ = "http://" + ip + "/" + file_id_;
        is_first_block_ = false;
        LOG_INFO << "uploadFirstBlock, file_id_buf = " << file_id_buf << ", file_url = " << file_url_;

        return true;
    } else {
        LOG_ERROR << "uploadFirstBlock " <<  originalFilename_ <<  " failed" ;
        return false;
    }
    
}

bool FileUploadContext::appendBlock(const char* data, size_t size) {
    if (!connected_) {
        return false;
    }

    int result = storage_append_by_filebuff1(static_cast<ConnectionInfo *>(tracker_server_),
            static_cast<ConnectionInfo *>(storage_server_),
            data, static_cast<int64_t>(size), file_id_.c_str());

    return result == 0;
}

void FileUploadContext::disconnect() {
    if (connected_) {
        if (tracker_server_) {
            tracker_close_connection_ex(static_cast<ConnectionInfo *>(tracker_server_), 1);
        }
        fdfs_client_destroy();
        connected_ = false;
    }
} 
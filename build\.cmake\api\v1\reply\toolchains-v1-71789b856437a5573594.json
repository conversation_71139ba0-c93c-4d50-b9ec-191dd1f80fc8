{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include", "C:/msys64/ucrt64/include", "C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"], "linkDirectories": ["C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0", "C:/msys64/ucrt64/lib/gcc", "C:/msys64/ucrt64/x86_64-w64-mingw32/lib", "C:/msys64/ucrt64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "mingw32", "gcc", "mingwex", "kernel32"]}, "path": "C:/msys64/ucrt64/bin/gcc.exe", "version": "14.2.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/msys64/ucrt64/include/c++/14.2.0", "C:/msys64/ucrt64/include/c++/14.2.0/x86_64-w64-mingw32", "C:/msys64/ucrt64/include/c++/14.2.0/backward", "C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include", "C:/msys64/ucrt64/include", "C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"], "linkDirectories": ["C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0", "C:/msys64/ucrt64/lib/gcc", "C:/msys64/ucrt64/x86_64-w64-mingw32/lib", "C:/msys64/ucrt64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32"]}, "path": "C:/msys64/ucrt64/bin/g++.exe", "version": "14.2.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP"]}, {"compiler": {"implicit": {}, "path": "C:/msys64/ucrt64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}